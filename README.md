# Trade Data Dashboard

A comprehensive dashboard for visualizing and analyzing import/export trade data. This application allows administrators to upload Excel files containing trade data and generate interactive charts and tables for users to explore.

## Features

- **User Authentication**: Secure login system with role-based access control
- **Admin Dashboard**:
  - Upload Excel files with trade data
  - Manage users and their permissions
  - Configure access rights for specific countries and products
- **User Dashboard**:
  - Interactive line charts showing trade trends
  - Pie charts displaying distribution of imports/exports
  - Tabular data with filtering options
  - Data export functionality
- **Data Visualization**:
  - Country → Product → Import/Export data navigation
  - Time-series analysis with line charts
  - Distribution analysis with pie charts

## Tech Stack

- **Frontend & Backend**: Next.js 14 (App Router)
- **Database**: MongoDB Atlas
- **ODM**: Mongoose
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS
- **Charts**: Chart.js with React-Chartjs-2
- **Excel Processing**: xlsx
- **Deployment**: Alibaba Cloud ECS (Ubuntu)

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- MongoDB Atlas account
- Alibaba Cloud ECS instance (for production)

### Local Development Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd trade-data-dashboard
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables:
   - Update the `.env.local` file with your MongoDB connection string and other variables

4. Run the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

### Initial Setup

1. Create an admin user in MongoDB:
   ```javascript
   db.users.insertOne({
     username: "admin",
     email: "<EMAIL>",
     password: "<hashed-password>", // Use bcrypt to hash the password
     role: "admin",
     permissions: {
       countries: [],
       products: []
     },
     createdAt: new Date(),
     updatedAt: new Date()
   })
   ```

2. Login with the admin credentials
3. Upload trade data Excel files
4. Create regular users and configure their permissions

## Excel File Format

The Excel files should have the following columns:
- `country`: Country name
- `product`: Product name
- `type`: "import" or "export"
- `year`: Year (numeric)
- `month`: Month (1-12)
- `value`: Trade value (numeric)
- `quantity`: Quantity (numeric)
- `unit`: Unit of measurement (string)

## Deployment to Alibaba Cloud ECS

1. Set up an Ubuntu server on Alibaba Cloud ECS
2. Install Node.js, npm, and MongoDB
3. Clone the repository and install dependencies
4. Build the application:
   ```bash
   npm run build
   ```
5. Set up a process manager (PM2):
   ```bash
   npm install -g pm2
   pm2 start npm --name "trade-dashboard" -- start
   ```
6. Configure Nginx as a reverse proxy

## License

This project is licensed under the MIT License - see the LICENSE file for details.

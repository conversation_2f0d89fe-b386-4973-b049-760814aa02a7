// This script checks the unit prices in the database
require('dotenv').config();
const mongoose = require('mongoose');
const { Schema } = mongoose;

// Define TradeData schema
const TradeDataSchema = new Schema({
  date: Date,
  year: Number,
  month: Number,
  hsCode: String,
  hsName: String,
  type: String,
  company: String,
  exportPort: String,
  destination: String,
  destinationPort: String,
  product: String,
  manufacturer: String,
  buyer: String,
  description: String,
  quantity: Number,
  unit: String,
  unitPrice: Number,
  value: Number,
  uploadedBy: Schema.Types.ObjectId,
  fileName: String,
}, { timestamps: true });

// Connect to MongoDB
async function main() {
  try {
    // Use hardcoded MongoDB URI if environment variable is not available
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/trade-data';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Register the model
    const TradeData = mongoose.model('TradeData', TradeDataSchema);

    // Get all trade data
    const tradeData = await TradeData.find({});
    console.log(`Found ${tradeData.length} trade data records`);

    // Check unit prices
    let zeroUnitPrices = 0;
    let nonZeroUnitPrices = 0;
    let nullUnitPrices = 0;

    for (const data of tradeData) {
      if (data.unitPrice === null || data.unitPrice === undefined) {
        nullUnitPrices++;
      } else if (data.unitPrice === 0) {
        zeroUnitPrices++;
        console.log('Record with zero unit price:', {
          id: data._id,
          product: data.product,
          quantity: data.quantity,
          value: data.value,
          unitPrice: data.unitPrice,
          description: data.description
        });
      } else {
        nonZeroUnitPrices++;
      }
    }

    console.log('Unit price statistics:');
    console.log(`- Zero unit prices: ${zeroUnitPrices}`);
    console.log(`- Non-zero unit prices: ${nonZeroUnitPrices}`);
    console.log(`- Null unit prices: ${nullUnitPrices}`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

main();

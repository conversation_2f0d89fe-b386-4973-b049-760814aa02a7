#!/bin/bash

# Deployment script for Alibaba Cloud ECS
# This script should be run on the server

# Exit on error
set -e

# Configuration
APP_DIR="/var/www/trade-dashboard"
REPO_URL="<your-repository-url>"
BRANCH="main"

# Update system
echo "Updating system packages..."
sudo apt-get update
sudo apt-get upgrade -y

# Install dependencies if not already installed
if ! command -v node &> /dev/null; then
    echo "Installing Node.js..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2..."
    sudo npm install -g pm2
fi

if ! command -v nginx &> /dev/null; then
    echo "Installing Nginx..."
    sudo apt-get install -y nginx
fi

# Create app directory if it doesn't exist
if [ ! -d "$APP_DIR" ]; then
    echo "Creating application directory..."
    sudo mkdir -p $APP_DIR
    sudo chown -R $USER:$USER $APP_DIR
fi

# Clone or pull repository
if [ -d "$APP_DIR/.git" ]; then
    echo "Pulling latest changes..."
    cd $APP_DIR
    git pull origin $BRANCH
else
    echo "Cloning repository..."
    git clone --branch $BRANCH $REPO_URL $APP_DIR
    cd $APP_DIR
fi

# Install dependencies
echo "Installing dependencies..."
npm install

# Build application
echo "Building application..."
npm run build

# Configure Nginx
if [ ! -f "/etc/nginx/sites-available/trade-dashboard" ]; then
    echo "Configuring Nginx..."
    sudo tee /etc/nginx/sites-available/trade-dashboard > /dev/null << EOF
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

    sudo ln -s /etc/nginx/sites-available/trade-dashboard /etc/nginx/sites-enabled/
    sudo nginx -t
    sudo systemctl restart nginx
fi

# Create uploads directory
mkdir -p $APP_DIR/uploads
chmod 755 $APP_DIR/uploads

# Start or restart application with PM2
if pm2 list | grep -q "trade-dashboard"; then
    echo "Restarting application..."
    pm2 restart trade-dashboard
else
    echo "Starting application..."
    pm2 start npm --name "trade-dashboard" -- start
    pm2 save
    pm2 startup
fi

echo "Deployment completed successfully!"

// Migration script to add exporter and importer fields to existing trade data
require('dotenv').config({ path: '.env.local' });
const mongoose = require('mongoose');

async function migrateExporterImporterFields() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    const db = mongoose.connection.db;
    const collection = db.collection('tradedatas');

    console.log('Starting migration to add exporter and importer fields...');

    // Get total count for progress tracking
    const totalCount = await collection.countDocuments();
    console.log(`Total documents to process: ${totalCount}`);

    let processedCount = 0;
    const batchSize = 1000;

    // Process documents in batches
    const cursor = collection.find({});

    while (await cursor.hasNext()) {
      const batch = [];

      // Collect batch of documents
      for (let i = 0; i < batchSize && await cursor.hasNext(); i++) {
        const doc = await cursor.next();
        batch.push(doc);
      }

      // Process batch
      const bulkOps = batch.map(doc => {
        const updateFields = {};

        // Add exporter and importer fields if they don't exist
        if (!doc.hasOwnProperty('exporter')) {
          updateFields.exporter = '';
        }
        if (!doc.hasOwnProperty('importer')) {
          updateFields.importer = '';
        }

        // Only create update operation if there are fields to update
        if (Object.keys(updateFields).length > 0) {
          return {
            updateOne: {
              filter: { _id: doc._id },
              update: { $set: updateFields }
            }
          };
        }
        return null;
      }).filter(op => op !== null);

      // Execute bulk update if there are operations
      if (bulkOps.length > 0) {
        await collection.bulkWrite(bulkOps);
        console.log(`Updated ${bulkOps.length} documents in current batch`);
      }

      processedCount += batch.length;
      console.log(`Progress: ${processedCount}/${totalCount} (${((processedCount/totalCount)*100).toFixed(1)}%)`);
    }

    console.log('Migration completed successfully!');
    console.log(`Total documents processed: ${processedCount}`);

    // Verify the migration
    console.log('\nVerifying migration...');
    const sampleDocs = await collection.find({}).limit(5).toArray();
    console.log('Sample documents after migration:');
    sampleDocs.forEach((doc, index) => {
      console.log(`Document ${index + 1}:`);
      console.log(`  - _id: ${doc._id}`);
      console.log(`  - type: ${doc.type}`);
      console.log(`  - company: ${doc.company || 'N/A'}`);
      console.log(`  - exporter: ${doc.exporter || 'N/A'}`);
      console.log(`  - importer: ${doc.importer || 'N/A'}`);
      console.log(`  - product: ${doc.product || 'N/A'}`);
      console.log('');
    });

  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
migrateExporterImporterFields();

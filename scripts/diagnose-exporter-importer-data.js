// Diagnostic script to check exporter and importer data in the database
require('dotenv').config({ path: '.env.local' });
const mongoose = require('mongoose');

async function diagnoseExporterImporterData() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    const db = mongoose.connection.db;
    const collection = db.collection('tradedatas');

    console.log('\n=== DIAGNOSTIC REPORT: EXPORTER/IMPORTER DATA ===\n');

    // Get total count
    const totalCount = await collection.countDocuments();
    console.log(`Total trade data records: ${totalCount}`);

    // Check for exporter/importer fields existence
    const withExporterField = await collection.countDocuments({ exporter: { $exists: true } });
    const withImporterField = await collection.countDocuments({ importer: { $exists: true } });

    console.log(`Records with 'exporter' field: ${withExporterField}`);
    console.log(`Records with 'importer' field: ${withImporterField}`);

    // Check for non-empty exporter/importer data
    const withExporterData = await collection.countDocuments({
      exporter: { $exists: true, $ne: '', $ne: null }
    });
    const withImporterData = await collection.countDocuments({
      importer: { $exists: true, $ne: '', $ne: null }
    });

    console.log(`Records with non-empty 'exporter' data: ${withExporterData}`);
    console.log(`Records with non-empty 'importer' data: ${withImporterData}`);

    // Sample records with your specific data
    console.log('\n=== SAMPLE RECORDS ===\n');

    // Look for records that might match your sample data
    const sampleQuery = {
      $or: [
        { product: /DHA algae oil/i },
        { hsCode: '15156000' },
        { destination: /United States/i },
        { company: /Dsm Vitamins/i }
      ]
    };

    const matchingRecords = await collection.find(sampleQuery).limit(5).toArray();

    if (matchingRecords.length > 0) {
      console.log(`Found ${matchingRecords.length} records matching your sample data:`);
      matchingRecords.forEach((record, index) => {
        console.log(`\nRecord ${index + 1}:`);
        console.log(`  - _id: ${record._id}`);
        console.log(`  - year: ${record.year}`);
        console.log(`  - month: ${record.month}`);
        console.log(`  - hsCode: ${record.hsCode}`);
        console.log(`  - hsName: ${record.hsName}`);
        console.log(`  - type: ${record.type}`);
        console.log(`  - company: ${record.company || 'EMPTY'}`);
        console.log(`  - exporter: ${record.exporter || 'EMPTY'}`);
        console.log(`  - importer: ${record.importer || 'EMPTY'}`);
        console.log(`  - destination: ${record.destination}`);
        console.log(`  - product: ${record.product}`);
        console.log(`  - quantity: ${record.quantity}`);
        console.log(`  - unitPrice: ${record.unitPrice}`);
        console.log(`  - value: ${record.value}`);
        console.log(`  - buyer: ${record.buyer || 'EMPTY'}`);
        console.log(`  - manufacturer: ${record.manufacturer || 'EMPTY'}`);
        console.log(`  - fileName: ${record.fileName}`);
      });
    } else {
      console.log('No records found matching your sample data criteria.');

      // Show some recent records instead
      console.log('\nShowing 3 most recent records:');
      const recentRecords = await collection.find({}).sort({ _id: -1 }).limit(3).toArray();

      recentRecords.forEach((record, index) => {
        console.log(`\nRecord ${index + 1}:`);
        console.log(`  - _id: ${record._id}`);
        console.log(`  - year: ${record.year}`);
        console.log(`  - month: ${record.month}`);
        console.log(`  - type: ${record.type}`);
        console.log(`  - company: ${record.company || 'EMPTY'}`);
        console.log(`  - exporter: ${record.exporter || 'EMPTY'}`);
        console.log(`  - importer: ${record.importer || 'EMPTY'}`);
        console.log(`  - destination: ${record.destination}`);
        console.log(`  - product: ${record.product}`);
        console.log(`  - buyer: ${record.buyer || 'EMPTY'}`);
        console.log(`  - fileName: ${record.fileName}`);
      });
    }

    // Check field statistics
    console.log('\n=== FIELD STATISTICS ===\n');

    const fieldStats = await collection.aggregate([
      {
        $group: {
          _id: null,
          totalRecords: { $sum: 1 },
          companyEmpty: {
            $sum: {
              $cond: [{ $or: [{ $eq: ['$company', ''] }, { $eq: ['$company', null] }] }, 1, 0]
            }
          },
          exporterEmpty: {
            $sum: {
              $cond: [{ $or: [{ $eq: ['$exporter', ''] }, { $eq: ['$exporter', null] }] }, 1, 0]
            }
          },
          importerEmpty: {
            $sum: {
              $cond: [{ $or: [{ $eq: ['$importer', ''] }, { $eq: ['$importer', null] }] }, 1, 0]
            }
          },
          buyerEmpty: {
            $sum: {
              $cond: [{ $or: [{ $eq: ['$buyer', ''] }, { $eq: ['$buyer', null] }] }, 1, 0]
            }
          }
        }
      }
    ]).toArray();

    if (fieldStats.length > 0) {
      const stats = fieldStats[0];
      console.log(`Total records: ${stats.totalRecords}`);
      console.log(`Empty company fields: ${stats.companyEmpty} (${((stats.companyEmpty/stats.totalRecords)*100).toFixed(1)}%)`);
      console.log(`Empty exporter fields: ${stats.exporterEmpty} (${((stats.exporterEmpty/stats.totalRecords)*100).toFixed(1)}%)`);
      console.log(`Empty importer fields: ${stats.importerEmpty} (${((stats.importerEmpty/stats.totalRecords)*100).toFixed(1)}%)`);
      console.log(`Empty buyer fields: ${stats.buyerEmpty} (${((stats.buyerEmpty/stats.totalRecords)*100).toFixed(1)}%)`);
    }

    console.log('\n=== RECOMMENDATIONS ===\n');

    if (withExporterField === 0 || withImporterField === 0) {
      console.log('❌ ISSUE: Exporter/Importer fields are missing from the database schema.');
      console.log('   SOLUTION: Run the migration script to add these fields.');
    }

    if (withExporterData === 0 && withImporterData === 0) {
      console.log('❌ ISSUE: No exporter/importer data found in the database.');
      console.log('   SOLUTION: Re-upload your Excel files after the schema update.');
    }

    if (withExporterData > 0 || withImporterData > 0) {
      console.log('✅ SUCCESS: Some exporter/importer data found in the database.');
    }

  } catch (error) {
    console.error('Diagnostic failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the diagnostic
diagnoseExporterImporterData();

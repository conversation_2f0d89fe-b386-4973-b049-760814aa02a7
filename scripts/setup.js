/**
 * <PERSON><PERSON><PERSON> to set up the project
 * 
 * Usage:
 * node scripts/setup.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Check if uploads directory exists, create if not
const uploadsDir = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  console.log('Creating uploads directory...');
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Check if .env.local exists, create example if not
const envPath = path.join(__dirname, '..', '.env.local');
if (!fs.existsSync(envPath)) {
  console.log('Creating example .env.local file...');
  const exampleEnv = `# MongoDB Connection
MONGODB_URI=**************************************************************************************************************

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_key

# Upload Directory
UPLOAD_DIR=uploads`;

  fs.writeFileSync(envPath, exampleEnv);
}

// Install dotenv and other script dependencies
console.log('Installing script dependencies...');
try {
  execSync('npm install dotenv mongoose bcryptjs --no-save', { stdio: 'inherit' });
  console.log('Dependencies installed successfully');
} catch (error) {
  console.error('Error installing dependencies:', error);
}

console.log('\nSetup complete!');
console.log('\nNext steps:');
console.log('1. Update the .env.local file with your MongoDB connection string');
console.log('2. Run "node scripts/create-admin.js" to create an admin user');
console.log('3. Start the development server with "npm run dev"');

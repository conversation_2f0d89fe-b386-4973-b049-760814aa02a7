/**
 * <PERSON><PERSON><PERSON> to create a simple test Excel file
 * 
 * Usage:
 * node scripts/create-simple-test.js
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

// Simple test data
const testData = [
  {
    country: 'China',
    product: 'Electronics',
    type: 'import',
    year: 2023,
    month: 1,
    value: 1000000,
    quantity: 5000,
    unit: 'Units'
  },
  {
    country: 'USA',
    product: 'Machinery',
    type: 'export',
    year: 2023,
    month: 1,
    value: 750000,
    quantity: 300,
    unit: 'Tons'
  }
];

// Create Excel file
function createTestFile(filename) {
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(testData);
  
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Test Data');
  
  // Ensure the directory exists
  const dir = path.dirname(filename);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Write to file
  XLSX.writeFile(workbook, filename);
  console.log(`Test Excel file created: ${filename}`);
}

// Main function
function main() {
  const filename = path.join(__dirname, '..', 'test-data.xlsx');
  
  console.log('Creating simple test Excel file...');
  createTestFile(filename);
  console.log('Done!');
}

// Run the script
main();

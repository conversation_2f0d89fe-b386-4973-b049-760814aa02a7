/**
 * <PERSON><PERSON><PERSON> to create a template Excel file
 * 
 * Usage:
 * node scripts/create-template.js
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

// Template data
const templateData = [
  {
    country: 'China',
    product: 'Electronics',
    type: 'import',
    year: 2023,
    month: 1,
    value: 1000000,
    quantity: 5000,
    unit: 'Units'
  },
  {
    country: 'USA',
    product: 'Machinery',
    type: 'export',
    year: 2023,
    month: 1,
    value: 750000,
    quantity: 300,
    unit: 'Tons'
  }
];

// Create Excel file
function createTemplateFile(filename) {
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(templateData);
  
  // Add column headers with descriptions
  const headerComment = {
    country: 'Country name (e.g., China, USA)',
    product: 'Product category (e.g., Electronics, Machinery)',
    type: 'Either "import" or "export"',
    year: 'Year as a number (e.g., 2023)',
    month: 'Month as a number (1-12)',
    value: 'Trade value in currency',
    quantity: 'Quantity of goods',
    unit: 'Unit of measurement (e.g., Tons, Units)'
  };
  
  // Add comments to cells
  if (!worksheet.comments) worksheet.comments = [];
  
  Object.keys(headerComment).forEach((key, index) => {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: index });
    worksheet.comments[cellRef] = { t: headerComment[key] };
  });
  
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');
  
  // Ensure the directory exists
  const dir = path.dirname(filename);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Write to file
  XLSX.writeFile(workbook, filename);
  console.log(`Template Excel file created: ${filename}`);
}

// Main function
function main() {
  const filename = path.join(__dirname, '..', 'public', 'trade-data-template.xlsx');
  
  console.log('Creating template Excel file...');
  createTemplateFile(filename);
  console.log('Done!');
}

// Run the script
main();

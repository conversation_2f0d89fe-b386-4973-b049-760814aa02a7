/**
 * <PERSON><PERSON><PERSON> to create a sample Excel file for testing
 * 
 * Usage:
 * node scripts/create-sample-data.js
 */

const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

// Sample data
const countries = ['China', 'USA', 'Japan', 'Germany', 'UK', 'France', 'Italy', 'Canada', 'South Korea', 'Australia'];
const products = ['Electronics', 'Machinery', 'Vehicles', 'Pharmaceuticals', 'Textiles', 'Chemicals', 'Food Products', 'Metals', 'Plastics', 'Furniture'];
const types = ['import', 'export'];
const years = [2020, 2021, 2022, 2023];
const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
const units = ['Tons', 'Kilograms', 'Units', 'Liters', 'Meters'];

// Generate random data
function generateRandomData(count) {
  const data = [];
  
  for (let i = 0; i < count; i++) {
    const country = countries[Math.floor(Math.random() * countries.length)];
    const product = products[Math.floor(Math.random() * products.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const year = years[Math.floor(Math.random() * years.length)];
    const month = months[Math.floor(Math.random() * months.length)];
    const value = Math.floor(Math.random() * 1000000) + 10000; // Random value between 10,000 and 1,010,000
    const quantity = Math.floor(Math.random() * 10000) + 100; // Random quantity between 100 and 10,100
    const unit = units[Math.floor(Math.random() * units.length)];
    
    data.push({
      country,
      product,
      type,
      year,
      month,
      value,
      quantity,
      unit
    });
  }
  
  return data;
}

// Create Excel file
function createExcelFile(data, filename) {
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.json_to_sheet(data);
  
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Trade Data');
  
  // Ensure the directory exists
  const dir = path.dirname(filename);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  // Write to file
  XLSX.writeFile(workbook, filename);
  console.log(`Excel file created: ${filename}`);
}

// Main function
function main() {
  const dataCount = 100; // Number of rows to generate
  const filename = path.join(__dirname, '..', 'sample-trade-data.xlsx');
  
  console.log(`Generating ${dataCount} rows of sample trade data...`);
  const data = generateRandomData(dataCount);
  
  createExcelFile(data, filename);
  console.log('Done!');
}

// Run the script
main();

'use client';

import React, { useState, useRef, useEffect, forwardRef } from 'react';

interface SearchableSelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {
  label?: string;
  error?: string;
  options: { value: string; label: string; disabled?: boolean }[];
  fullWidth?: boolean;
  onChange: (value: string) => void;
  value: string;
  placeholder?: string;
  onDisabledOptionSelected?: (value: string, label: string) => void;
}

const SearchableSelect = forwardRef<HTMLDivElement, SearchableSelectProps>(
  ({
    label,
    error,
    options,
    fullWidth = false,
    className = '',
    onChange,
    value,
    placeholder = 'Search...',
    onDisabledOptionSelected,
    ...props
  }, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);
    const optionsRef = useRef<HTMLUListElement>(null);

    // Filter options based on search term
    const filteredOptions = options.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort options - put exact matches first, then starts with, then includes
    const sortedFilteredOptions = [...filteredOptions].sort((a, b) => {
      const aLower = a.label.toLowerCase();
      const bLower = b.label.toLowerCase();
      const searchLower = searchTerm.toLowerCase();

      // Empty search term - keep original order
      if (!searchLower) return 0;

      // Exact match for a
      if (aLower === searchLower) return -1;
      // Exact match for b
      if (bLower === searchLower) return 1;

      // a starts with search term
      if (aLower.startsWith(searchLower) && !bLower.startsWith(searchLower)) return -1;
      // b starts with search term
      if (!aLower.startsWith(searchLower) && bLower.startsWith(searchLower)) return 1;

      // Both or neither start with search term - keep original order
      return 0;
    });

    // Get the selected option label
    const selectedOption = options.find(option => option.value === value);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Focus search input when dropdown opens
    useEffect(() => {
      if (isOpen && searchInputRef.current) {
        searchInputRef.current.focus();
        setHighlightedIndex(-1);
      }
    }, [isOpen]);

    // Reset highlighted index when filtered options change
    useEffect(() => {
      setHighlightedIndex(-1);
    }, [sortedFilteredOptions]);

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev =>
            prev < sortedFilteredOptions.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => (prev > 0 ? prev - 1 : 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < sortedFilteredOptions.length) {
            handleSelect(sortedFilteredOptions[highlightedIndex].value);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          break;
      }
    };

    const handleSelect = (optionValue: string) => {
      // Find the option to check if it's disabled
      const option = options.find(opt => opt.value === optionValue);

      if (option?.disabled) {
        // If option is disabled and handler is provided, call it
        if (onDisabledOptionSelected) {
          onDisabledOptionSelected(optionValue, option.label);
        }
        // Don't close dropdown or change value for disabled options
        return;
      }

      // For enabled options, proceed normally
      onChange(optionValue);
      setIsOpen(false);
      setSearchTerm('');
    };

    const containerClasses = `
      relative ${fullWidth ? 'w-full' : ''} mb-4
    `;

    const triggerClasses = `
      block w-full px-4 py-2 border rounded-md shadow-sm
      focus:ring-blue-500 focus:border-blue-500 focus:outline-none
      ${error ? 'border-red-500' : 'border-gray-300'}
      ${className}
      cursor-pointer bg-white
    `;

    const dropdownClasses = `
      absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg
      max-h-60 overflow-y-auto
    `;

    return (
      <div className={containerClasses} ref={ref}>
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}

        <div ref={dropdownRef} className="relative">
          {/* Trigger button */}
          <div
            className={triggerClasses}
            onClick={() => setIsOpen(!isOpen)}
          >
            {selectedOption ? selectedOption.label : 'Please select'}
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </div>
          </div>

          {/* Dropdown */}
          {isOpen && (
            <div className={dropdownClasses}>
              {/* Search input */}
              <div className="sticky top-0 p-2 bg-white border-b border-gray-200">
                <input
                  ref={searchInputRef}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder={placeholder}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
              </div>

              {/* Options list */}
              <ul ref={optionsRef} className="py-1">
                {sortedFilteredOptions.length > 0 ? (
                  sortedFilteredOptions.map((option, index) => (
                    <li
                      key={option.value}
                      className={`px-4 py-2
                        ${option.disabled
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'hover:bg-blue-100 cursor-pointer'
                        }
                        ${option.value === value ? 'bg-blue-50 font-medium' : ''}
                        ${index === highlightedIndex && !option.disabled ? 'bg-blue-100' : ''}`}
                      onClick={() => handleSelect(option.value)}
                      onMouseEnter={() => !option.disabled && setHighlightedIndex(index)}
                      title={option.disabled ? 'Please contact an administrator for access' : ''}
                    >
                      {option.label}
                    </li>
                  ))
                ) : (
                  <li className="px-4 py-2 text-gray-500">No matches found</li>
                )}
              </ul>
            </div>
          )}
        </div>

        {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
      </div>
    );
  }
);

SearchableSelect.displayName = 'SearchableSelect';

export default SearchableSelect;

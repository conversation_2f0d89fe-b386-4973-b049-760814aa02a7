'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { Pie } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import '@/styles/pieChart.css';

ChartJS.register(ArcElement, Tooltip, Legend, ChartDataLabels);

interface PieChartProps {
  data: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
      borderColor?: string[];
      borderWidth?: number;
    }[];
  };
  title?: string;
}

// Custom Legend Component
const CustomLegend: React.FC<{
  labels: string[];
  backgroundColor: string[];
  data: number[];
}> = ({ labels, backgroundColor, data }) => {
  // Calculate total for percentages
  const total = data.reduce((acc, value) => acc + value, 0);

  // Create items array with all data
  const items = labels.map((label, index) => ({
    label,
    color: backgroundColor[index],
    value: data[index],
    percentage: ((data[index] / total) * 100).toFixed(1),
    isOthers: label === 'Others'
  }));

  // Sort items to ensure "Others" is always at the end
  items.sort((a, b) => {
    // If one is "Others", it goes last
    if (a.isOthers) return 1;
    if (b.isOthers) return -1;

    // Otherwise sort by value (descending)
    return b.value - a.value;
  });

  return (
    <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-2">
      {items.map((item, index) => {
        // Truncate long labels
        const displayLabel = item.label.length > 25 ? item.label.substring(0, 22) + '...' : item.label;

        return (
          <div key={index} className="flex items-center text-sm">
            <span
              className="inline-block w-3 h-3 mr-2 rounded-full"
              style={{ backgroundColor: item.color }}
            ></span>
            <span className="truncate" title={item.label}>
              {displayLabel} ({item.percentage}%)
            </span>
          </div>
        );
      })}
    </div>
  );
};

const PieChart: React.FC<PieChartProps> = ({ data: originalData, title }) => {
  const [chartInstance, setChartInstance] = useState<any>(null);

  // Process data to ensure consistent ordering and "Others" at the end
  const processData = (data: any) => {
    if (!data || !data.labels || !data.datasets || data.datasets.length === 0) {
      console.log('PIE CHART COMPONENT DEBUG - Invalid data format, returning as is');
      return data;
    }

    console.log('PIE CHART COMPONENT DEBUG - Processing data with labels:', data.labels);

    const labels = [...data.labels];
    const values = [...data.datasets[0].data];
    const colors = [...data.datasets[0].backgroundColor];

    // Create items array with all data
    const items = labels.map((label, index) => ({
      label,
      value: values[index],
      color: colors[index],
      isOthers: label === 'Others'
    }));

    console.log('PIE CHART COMPONENT DEBUG - Items before sorting:', items);

    // Sort items to ensure "Others" is always at the end
    items.sort((a, b) => {
      // If one is "Others", it goes last
      if (a.isOthers) return 1;
      if (b.isOthers) return -1;

      // Otherwise sort by value (descending)
      return b.value - a.value;
    });

    console.log('PIE CHART COMPONENT DEBUG - Items after sorting:', items);

    // Reconstruct the data object
    const processedData = {
      labels: items.map(item => item.label),
      datasets: [{
        data: items.map(item => item.value),
        backgroundColor: items.map(item => item.color),
        borderWidth: data.datasets[0].borderWidth,
        borderColor: data.datasets[0].borderColor
      }]
    };

    console.log('PIE CHART COMPONENT DEBUG - Processed data:', processedData);
    return processedData;
  };

  // Process the data
  const data = processData(originalData);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        top: 10,
        right: 10,
        bottom: 10,
        left: 10
      }
    },
    // Set the pie chart to take up more space
    radius: '90%',
    plugins: {
      legend: {
        position: 'chartArea' as const,
        align: 'start' as const,
        display: false, // Hide the default legend, we'll create a custom one
        labels: {
          padding: 15,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 8,
          font: {
            size: 11
          },
          // Generate shorter labels if they're too long
          generateLabels: function(chart: any) {
            const original = ChartJS.overrides.pie.plugins.legend.labels.generateLabels;
            const labels = original.call(this, chart);

            return labels.map((label: any) => {
              // Truncate long labels
              if (label.text && label.text.length > 25) {
                label.text = label.text.substring(0, 22) + '...';
              }
              return label;
            });
          }
        },
        // Make legend scrollable if too many items
        onClick: function(_e: any, legendItem: any, legend: any) {
          const index = legendItem.index;
          const ci = legend.chart;

          if (ci.isDatasetVisible(0)) {
            ci.hide(0, index);
            legendItem.hidden = true;
          } else {
            ci.show(0, index);
            legendItem.hidden = false;
          }

          ci.update();
        }
      },
      title: {
        display: !!title,
        text: title,
      },
      // Add percentage labels
      datalabels: {
        formatter: (value: number, ctx: any) => {
          const dataset = ctx.chart.data.datasets[0];
          const total = dataset.data.reduce((acc: number, data: number) => acc + data, 0);
          const percentage = ((value / total) * 100).toFixed(1) + '%';
          return percentage;
        },
        color: '#fff',
        font: {
          weight: 'bold' as const,
          size: 12
        },
        textAlign: 'center',
        textStrokeColor: '#000',
        textStrokeWidth: 1,
        // Only show labels for segments that are at least 5% of the total
        display: (ctx: any) => {
          const dataset = ctx.chart.data.datasets[0];
          const total = dataset.data.reduce((acc: number, data: number) => acc + data, 0);
          const value = dataset.data[ctx.dataIndex];
          return (value / total) * 100 >= 5;
        }
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label || '';
            const value = context.raw || 0;
            const dataset = context.chart.data.datasets[0];
            const total = dataset.data.reduce((acc: number, data: number) => acc + data, 0);
            const percentage = ((value / total) * 100).toFixed(1) + '%';
            return `${label}: ${value.toLocaleString()} (${percentage})`;
          }
        }
      }
    },
  };

  // Get chart instance for custom legend
  const chartRef = useRef<any>(null);

  useEffect(() => {
    if (chartRef.current) {
      setChartInstance(chartRef.current);
    }
  }, [chartRef]);

  return (
    <div className="h-[518px] pie-chart-container">
      <div className="h-[336px] flex items-center justify-center">
        <Pie
          ref={chartRef}
          options={options}
          data={data}
        />
      </div>
      <div className="legend-container h-[182px] overflow-y-auto px-4 py-2">
        {data.labels && data.datasets && data.datasets[0] && (
          <CustomLegend
            labels={data.labels as string[]}
            backgroundColor={data.datasets[0].backgroundColor as string[]}
            data={data.datasets[0].data as number[]}
          />
        )}
      </div>
    </div>
  );
};

export default PieChart;

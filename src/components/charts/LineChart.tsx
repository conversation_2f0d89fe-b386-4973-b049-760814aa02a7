'use client';

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface LineChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }[];
  };
  title?: string;
}

const LineChart: React.FC<LineChartProps> = ({ data, title }) => {
  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: !!title,
        text: title,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      },
      // Disable datalabels plugin if it's being used
      datalabels: {
        display: false
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          // Include a dollar sign in the ticks
          callback: function(value: any) {
            // Format the value without showing decimal places
            return Math.round(value).toString();
          }
        }
      },
    },
    elements: {
      point: {
        radius: 4, // Show solid circle points on the line
        hoverRadius: 6, // Slightly larger on hover
        backgroundColor: 'white', // White fill
        borderWidth: 2, // Border width
      },
      line: {
        tension: 0, // No curve, straight lines
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow" style={{ height: '360px' }}>
      <Line options={{ ...options, maintainAspectRatio: false }} data={data} />
    </div>
  );
};

export default LineChart;

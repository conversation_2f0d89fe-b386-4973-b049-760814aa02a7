'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import Select from '@/components/ui/Select';
import SearchableSelect from '@/components/ui/SearchableSelect';
import Button from '@/components/ui/Button';
import Line<PERSON>hart from '@/components/charts/LineChart';
import Pie<PERSON>hart from '@/components/charts/PieChart';
import Modal from '@/components/ui/Modal';

const DashboardClient: React.FC = () => {
  const { data: session } = useSession();

  const [countries, setCountries] = useState<string[]>([]);
  const [products, setProducts] = useState<string[]>([]);
  const [hsCodes, setHsCodes] = useState<string[]>([]);
  const [companies, setCompanies] = useState<string[]>([]);
  const [years, setYears] = useState<number[]>([]);
  const [fileCountries, setFileCountries] = useState<string[]>([]);

  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedHsCode, setSelectedHsCode] = useState<string>('');
  const [selectedCompany, setSelectedCompany] = useState<string>('');
  const [selectedFileCountry, setSelectedFileCountry] = useState<string>('');
  const [selectedMonthChartYears, setSelectedMonthChartYears] = useState<string[]>([]);
  const [selectedYearChartYears, setSelectedYearChartYears] = useState<string[]>([]);

  // Product/Destination Distribution years
  const [selectedImportPieChartYear, setSelectedImportPieChartYear] = useState<string>('');
  const [selectedExportPieChartYear, setSelectedExportPieChartYear] = useState<string>('');

  // Company Distribution years
  const [selectedImportCompanyPieChartYear, setSelectedImportCompanyPieChartYear] = useState<string>('');
  const [selectedExportCompanyPieChartYear, setSelectedExportCompanyPieChartYear] = useState<string>('');

  // Buyer Distribution years
  const [selectedImportBuyerPieChartYear, setSelectedImportBuyerPieChartYear] = useState<string>('');
  const [selectedExportBuyerPieChartYear, setSelectedExportBuyerPieChartYear] = useState<string>('');

  const [tableData, setTableData] = useState<any[]>([]);
  const [monthLineChartData, setMonthLineChartData] = useState<any>(null);
  const [yearLineChartData, setYearLineChartData] = useState<any>(null);

  // 原始数据用于表格显示
  const [monthlyRawData, setMonthlyRawData] = useState<any[]>([]);
  const [yearlyRawData, setYearlyRawData] = useState<any[]>([]);

  // Product/Destination Distribution data
  const [importPieChartData, setImportPieChartData] = useState<any>(null);
  const [exportPieChartData, setExportPieChartData] = useState<any>(null);

  // Company Distribution data
  const [importCompanyPieChartData, setImportCompanyPieChartData] = useState<any>(null);
  const [exportCompanyPieChartData, setExportCompanyPieChartData] = useState<any>(null);

  // Buyer Distribution data
  const [importBuyerPieChartData, setImportBuyerPieChartData] = useState<any>(null);
  const [exportBuyerPieChartData, setExportBuyerPieChartData] = useState<any>(null);
  const [visibleRows, setVisibleRows] = useState<number>(10);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // 动态筛选器标签
  const [destinationOriginLabel, setDestinationOriginLabel] = useState<string>('Destination/Origin');

  // 图表显示状态
  const [showImportCharts, setShowImportCharts] = useState<boolean>(true);
  const [showExportCharts, setShowExportCharts] = useState<boolean>(true);

  // 检查是否为中国出口分析订阅类型
  const isChinaExportAnalysis = (): boolean => {
    if (!session || session.user.role === 'admin') {
      return true; // 管理员可以看到所有列
    }

    // 基于用户权限判断是否为中国出口分析订阅
    // 如果用户有export权限且fileCountries包含China，则认为是中国出口分析
    const hasExportPermission = Array.isArray(session?.user?.permissions?.types) &&
      session?.user?.permissions?.types.includes('export');
    const hasChinaPermission = Array.isArray(session?.user?.permissions?.fileCountries) &&
      session?.user?.permissions?.fileCountries.includes('China');

    return hasExportPermission && hasChinaPermission;
  };

  // 动态表格标题映射函数
  const getDynamicHeaders = () => {
    const isExport = selectedType === 'export';
    const isImport = selectedType === 'import';
    const showManufacturer = isChinaExportAnalysis();

    return {
      company: isExport ? 'Exporter' : isImport ? 'Importer' : 'Company',
      port: isExport ? 'Export Port' : isImport ? 'Import Port' : 'Export Port/Import Port',
      destinationOrigin: isExport ? 'Destination' : isImport ? 'Origin' : 'Destination/Origin',
      importerExporter: isExport ? 'Importer' : isImport ? 'Exporter' : 'Importer/Exporter',
      showManufacturer: showManufacturer
    };
  };

  // 权限对话框状态
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [permissionModalMessage, setPermissionModalMessage] = useState('');

  // 检查用户是否有权限访问特定选项
  const checkPermission = (type: 'countries' | 'products' | 'types' | 'years' | 'fileCountries' | 'origins' | 'destinations', value: string): boolean => {
    if (!session || session.user.role === 'admin') {
      return true; // 管理员有所有权限
    }

    // 检查用户是否有该类型的权限列表
    const permissions = session.user.permissions[type];
    if (!permissions || !Array.isArray(permissions)) {
      return false;
    }

    // 空值（如 'All Countries'）总是允许的
    if (!value) {
      return true;
    }

    // 检查值是否在权限列表中
    return permissions.includes(value);
  };

  // 生成动态标题的通用函数
  const generateDynamicTitle = (baseTitle: string): string => {
    const titleParts = [];
    if (selectedProduct) titleParts.push(selectedProduct);
    if (selectedFileCountry) titleParts.push(selectedFileCountry);
    if (selectedType) titleParts.push(selectedType.charAt(0).toUpperCase() + selectedType.slice(1));

    return titleParts.length > 0
      ? `${titleParts.join(' - ')} ${baseTitle}`
      : baseTitle;
  };

  // Handle callback when a disabled option is selected
  const handleDisabledOptionSelected = (type: string, value: string, label: string) => {
    let message = '';
    switch (type) {
      case 'countries':
        message = `You don't have permission to access data for the ${destinationOriginLabel} "${label}". Please contact an administrator for access.`;
        break;
      case 'fileCountries':
        message = `You don't have permission to access data for the Country "${label}". Please contact an administrator for access.`;
        break;
      case 'products':
        message = `You don't have permission to access data for the product "${label}". Please contact an administrator for access.`;
        break;
      case 'types':
        message = `You don't have permission to access "${label}" type data. Please contact an administrator for access.`;
        break;
      case 'years':
        message = `You don't have permission to access data for the year ${label}. Please contact an administrator for access.`;
        break;
      default:
        message = `You don't have permission to access the selected data. Please contact an administrator for access.`;
    }

    setPermissionModalMessage(message);
    setShowPermissionModal(true);
  };

  // Fetch metadata (countries, products, years)
  useEffect(() => {
    const fetchMetadata = async () => {
      try {
        console.log('Fetching metadata for dashboard');
        const response = await axios.get('/api/metadata');
        console.log('Metadata response:', response.data);
        setCountries(response.data.countries);
        setProducts(response.data.products);
        setHsCodes(response.data.hsCodes || []);
        setCompanies(response.data.companies || []);
        setYears(response.data.years || []);
        setFileCountries(response.data.fileCountries || []);

        // Set default selections if available
        if (response.data.countries.length > 0) {
          setSelectedCountry(response.data.countries[0]);
        }
        if (response.data.products.length > 0) {
          setSelectedProduct(response.data.products[0]);
        }
        if (response.data.years.length > 0) {
          // Sort years in descending order
          const sortedYears = [...response.data.years].sort((a, b) => b - a);

          // Filter years based on user permissions
          const permittedYears = sortedYears
            .map(year => year.toString())
            .filter(yearStr => checkPermission('years', yearStr));

          console.log('INIT DEBUG - All years:', sortedYears.map(year => year.toString()));
          console.log('INIT DEBUG - Permitted years:', permittedYears);

          // Set permitted years as default for month chart
          console.log('INIT DEBUG - Setting permitted years for monthly chart:', permittedYears);
          setSelectedMonthChartYears(permittedYears);

          // Set the last 3 permitted years (or all if less than 3) for year chart
          const yearsToSelect = permittedYears.slice(0, 3);
          setSelectedYearChartYears(yearsToSelect);

          // Set the most recent year as default for Product/Destination Distribution
          // We use empty string to represent "All Years"
          setSelectedImportPieChartYear('');
          setSelectedExportPieChartYear('');

          // Set the most recent year as default for Company Distribution
          // We use empty string to represent "All Years"
          setSelectedImportCompanyPieChartYear('');
          setSelectedExportCompanyPieChartYear('');

          // Set the most recent year as default for Buyer Distribution
          // We use empty string to represent "All Years"
          setSelectedImportBuyerPieChartYear('');
          setSelectedExportBuyerPieChartYear('');
        }
      } catch (err) {
        console.error('Error fetching metadata:', err);
        setError('Failed to load filter options');
      }
    };

    fetchMetadata();
  }, []);

  // Fetch data based on filters
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Check if user has permission for selected type
      if (selectedType && session?.user?.role !== 'admin') {
        const hasPermission = Array.isArray(session?.user?.permissions?.types) &&
          session?.user?.permissions?.types.includes(selectedType);

        if (!hasPermission) {
          console.log(`User does not have permission for ${selectedType} data`);
          setTableData([]);
          setIsLoading(false);
          setError(`You don't have permission to access ${selectedType} data. Please contact an administrator.`);
          return;
        }
      }

      // Fetch table data
      const response = await axios.get('/api/data', {
        params: {
          country: selectedCountry || undefined,
          fileCountry: selectedFileCountry || undefined,
          product: selectedProduct || undefined,
          type: selectedType || undefined,
        },
      });

      console.log('Trade Data API response:', response.data);

      // 确保正确处理 API 响应格式
      if (response.data && response.data.data) {
        // API 返回的是 { data: [...], pagination: {...} } 格式
        setTableData(response.data.data);
      } else if (Array.isArray(response.data)) {
        // 如果 API 直接返回数组
        setTableData(response.data);
      } else {
        // 如果返回的数据格式不符合预期，设置为空数组
        console.error('Unexpected data format from API:', response.data);
        setTableData([]);
      }

      // Refresh all chart data
      console.log('Refreshing all chart data after filter change');

      // Fetch monthly chart data
      await fetchMonthlyChart();

      // Fetch yearly chart data
      await fetchYearlyChartWithYears(selectedYearChartYears);

      // Fetch product/destination distribution data
      await fetchPieChart();

      // Fetch company distribution data
      await fetchCompanyPieChart();

      // Fetch buyer distribution data
      await fetchBuyerPieChart();

      setIsLoading(false);
    } catch (err: any) {
      console.log('Error fetching data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for data');
        setTableData([]);
        setError('You do not have permission to access this data. Please contact an administrator.');
      } else {
        // For other errors, set the general error message
        setError('Failed to load data');
      }
      setIsLoading(false);
    }
  };

  // Handle download
  const handleDownload = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Check if user has permission for selected type
      if (selectedType && session?.user?.role !== 'admin') {
        const hasPermission = Array.isArray(session?.user?.permissions?.types) &&
          session?.user?.permissions?.types.includes(selectedType);

        if (!hasPermission) {
          console.log(`User does not have permission to download ${selectedType} data`);
          setIsLoading(false);
          setError(`You don't have permission to download ${selectedType} data. Please contact an administrator.`);
          return;
        }
      }

      const response = await axios.get('/api/download', {
        params: {
          country: selectedCountry || undefined,
          fileCountry: selectedFileCountry || undefined,
          product: selectedProduct || undefined,
          type: selectedType || undefined,
        },
        responseType: 'blob',
      });

      // Create a URL for the blob
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'trade_data.xlsx');
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);
      setIsLoading(false);
    } catch (err: any) {
      console.log('Error downloading data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for downloading data');
        setError('You do not have permission to download this data. Please contact an administrator.');
      } else {
        // For other errors, set the general error message
        setError('Failed to download data');
      }
      setIsLoading(false);
    }
  };

  // Initialize charts when component mounts
  useEffect(() => {
    // Only initialize charts once when component mounts
    if (selectedMonthChartYears.length > 0 && selectedYearChartYears.length > 0) {
      // Fetch each chart independently
      fetchMonthlyChart();
      fetchYearlyChartWithYears(selectedYearChartYears);
      fetchPieChart();
      fetchCompanyPieChart();
      fetchBuyerPieChart();
    }
  }, [selectedMonthChartYears, selectedYearChartYears]);

  // 更新Destination/Origin标签根据选择的Type
  useEffect(() => {
    if (selectedType === 'import') {
      setDestinationOriginLabel('Origin');
    } else if (selectedType === 'export') {
      setDestinationOriginLabel('Destination');
    } else {
      setDestinationOriginLabel('Destination/Origin');
    }
  }, [selectedType]);

  // 更新图表显示状态根据选择的Type
  useEffect(() => {
    if (selectedType === 'import') {
      setShowImportCharts(true);
      setShowExportCharts(false);
    } else if (selectedType === 'export') {
      setShowImportCharts(false);
      setShowExportCharts(true);
    } else {
      setShowImportCharts(true);
      setShowExportCharts(true);
    }
  }, [selectedType]);

  // Fetch monthly chart data
  const fetchMonthlyChart = async () => {
    try {
      // Make a copy of the current years to avoid state update issues
      const currentYears = [...selectedMonthChartYears];

      console.log('MONTHLY CHART DEBUG - Current years before fetch:', currentYears);

      // Only fetch if we have years selected
      if (currentYears.length === 0) {
        console.log('MONTHLY CHART DEBUG - No years selected, clearing data');
        setMonthLineChartData(null);
        return;
      }

      // Check if user has permission for selected types
      const hasImportPermission = session?.user?.role === 'admin' ||
        (Array.isArray(session?.user?.permissions?.types) &&
        session?.user?.permissions?.types.includes('import'));

      const hasExportPermission = session?.user?.role === 'admin' ||
        (Array.isArray(session?.user?.permissions?.types) &&
        session?.user?.permissions?.types.includes('export'));

      // If a specific type is selected, check permission for that type
      if (selectedType) {
        const hasPermission = selectedType === 'import' ? hasImportPermission : hasExportPermission;
        if (!hasPermission) {
          console.log(`User does not have permission for ${selectedType} data`);
          setMonthLineChartData(null);
          return;
        }
      } else if (!hasImportPermission && !hasExportPermission) {
        // If no specific type is selected, user needs permission for at least one type
        console.log('User does not have permission for any data type');
        setMonthLineChartData(null);
        return;
      }

      console.log('MONTHLY CHART DEBUG - Fetching with years:', currentYears);
      console.log('MONTHLY CHART DEBUG - Fetching with type:', selectedType || 'All Types');

      // Fetch month line chart data with the current years
      const monthLineChartResponse = await axios.get('/api/charts', {
        params: {
          country: selectedCountry || undefined,
          fileCountry: selectedFileCountry || undefined,
          product: selectedProduct || undefined,
          type: selectedType || undefined,
          years: currentYears.join(','),
          chartType: 'monthLine',
        },
      });

      console.log('MONTHLY CHART DEBUG - Data received:', monthLineChartResponse.data);
      setMonthLineChartData(monthLineChartResponse.data);

      // 保存原始数据用于表格显示
      if (monthLineChartResponse.data && monthLineChartResponse.data.rawData) {
        setMonthlyRawData(monthLineChartResponse.data.rawData);
      } else {
        setMonthlyRawData([]);
      }
    } catch (err: any) {
      console.log('Error fetching monthly chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for monthly chart data');
        setMonthLineChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load monthly chart data');
      }
    }
  };

  // Fetch yearly chart data with specific years
  const fetchYearlyChartWithYears = async (years: string[]) => {
    try {
      // Only fetch if we have years selected
      if (years.length === 0) {
        console.log('No years selected, clearing year line chart data');
        setYearLineChartData(null);
        return;
      }

      // Check if user has permission for selected types
      const hasImportPermission = session?.user?.role === 'admin' ||
        (Array.isArray(session?.user?.permissions?.types) &&
        session?.user?.permissions?.types.includes('import'));

      const hasExportPermission = session?.user?.role === 'admin' ||
        (Array.isArray(session?.user?.permissions?.types) &&
        session?.user?.permissions?.types.includes('export'));

      // If a specific type is selected, check permission for that type
      if (selectedType) {
        const hasPermission = selectedType === 'import' ? hasImportPermission : hasExportPermission;
        if (!hasPermission) {
          console.log(`User does not have permission for ${selectedType} data`);
          setYearLineChartData(null);
          return;
        }
      } else if (!hasImportPermission && !hasExportPermission) {
        // If no specific type is selected, user needs permission for at least one type
        console.log('User does not have permission for any data type');
        setYearLineChartData(null);
        return;
      }

      // Log the years and type being requested
      console.log('Fetching yearly chart data with years:', years);
      console.log('Fetching yearly chart data with type:', selectedType || 'All Types');

      // Fetch year line chart data with the provided years
      const yearLineChartResponse = await axios.get('/api/charts', {
        params: {
          country: selectedCountry || undefined,
          fileCountry: selectedFileCountry || undefined,
          product: selectedProduct || undefined,
          type: selectedType || undefined,
          years: years.join(','),
          chartType: 'yearLine',
        },
      });

      console.log('Yearly chart data received:', yearLineChartResponse.data);
      setYearLineChartData(yearLineChartResponse.data);

      // 保存原始数据用于表格显示
      if (yearLineChartResponse.data && yearLineChartResponse.data.rawData) {
        setYearlyRawData(yearLineChartResponse.data.rawData);
      } else {
        setYearlyRawData([]);
      }
    } catch (err: any) {
      console.log('Error fetching yearly chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for yearly chart data');
        setYearLineChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load yearly chart data');
      }
    }
  };

  // Fetch import pie chart data with specific year
  const fetchImportPieChartWithYear = async (yearValue: string) => {
    try {
      console.log('IMPORT PIE CHART DEBUG - Fetching import pie chart with direct year value:', yearValue);
      console.log('IMPORT PIE CHART DEBUG - Destination/Origin:', selectedCountry || 'All Destinations/Origins');
      console.log('IMPORT PIE CHART DEBUG - Product:', selectedProduct || 'All Products');

      // Check if user has permission for import type
      if (session?.user?.role !== 'admin' &&
          Array.isArray(session?.user?.permissions?.types) &&
          !session?.user?.permissions?.types.includes('import')) {
        console.log('User does not have permission for import data');
        setImportPieChartData(null);
        return;
      }

      // Create params object
      const params: any = {
        country: selectedCountry || undefined,
        fileCountry: selectedFileCountry || undefined,
        product: selectedProduct || undefined,
        type: 'import', // Always fetch import data only
        chartType: 'pie',
      };

      // Always include year parameter, even if it's empty string (which means "All Years")
      // This ensures consistent behavior between selecting a specific year and "All Years"
      params.year = yearValue;

      console.log('IMPORT PIE CHART DEBUG - Final params:', params);
      console.log('IMPORT PIE CHART DEBUG - API URL:', '/api/charts');

      // Fetch import pie chart data
      const importPieChartResponse = await axios.get('/api/charts', { params });

      console.log('IMPORT PIE CHART DEBUG - Response status:', importPieChartResponse.status);
      console.log('IMPORT PIE CHART DEBUG - Data received from API:', importPieChartResponse.data);

      // Only set the import data
      if (importPieChartResponse.data.import) {
        setImportPieChartData(importPieChartResponse.data.import);
      } else {
        setImportPieChartData(null);
      }
    } catch (err: any) {
      console.log('Error fetching import pie chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for import pie chart data');
        setImportPieChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load import pie chart data');
      }
    }
  };

  // Fetch export pie chart data with specific year
  const fetchExportPieChartWithYear = async (yearValue: string) => {
    try {
      console.log('EXPORT PIE CHART DEBUG - Fetching export pie chart with direct year value:', yearValue);
      console.log('EXPORT PIE CHART DEBUG - Destination/Origin:', selectedCountry || 'All Destinations/Origins');
      console.log('EXPORT PIE CHART DEBUG - Product:', selectedProduct || 'All Products');

      // Check if user has permission for export type
      if (session?.user?.role !== 'admin' &&
          Array.isArray(session?.user?.permissions?.types) &&
          !session?.user?.permissions?.types.includes('export')) {
        console.log('User does not have permission for export data');
        setExportPieChartData(null);
        return;
      }

      // Create params object
      const params: any = {
        country: selectedCountry || undefined,
        fileCountry: selectedFileCountry || undefined,
        product: selectedProduct || undefined,
        type: 'export', // Always fetch export data only
        chartType: 'pie',
      };

      // Always include year parameter, even if it's empty string (which means "All Years")
      // This ensures consistent behavior between selecting a specific year and "All Years"
      params.year = yearValue;

      console.log('EXPORT PIE CHART DEBUG - Final params:', params);
      console.log('EXPORT PIE CHART DEBUG - API URL:', '/api/charts');

      // Fetch export pie chart data
      const exportPieChartResponse = await axios.get('/api/charts', { params });

      console.log('EXPORT PIE CHART DEBUG - Response status:', exportPieChartResponse.status);
      console.log('EXPORT PIE CHART DEBUG - Data received from API:', exportPieChartResponse.data);

      // Only set the export data
      if (exportPieChartResponse.data.export) {
        setExportPieChartData(exportPieChartResponse.data.export);
      } else {
        setExportPieChartData(null);
      }
    } catch (err: any) {
      console.log('Error fetching export pie chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for export pie chart data');
        setExportPieChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load export pie chart data');
      }
    }
  };

  // Fetch pie chart data
  const fetchPieChart = async () => {
    try {
      // Fetch import and export pie chart data with the current year values
      await fetchImportPieChartWithYear(selectedImportPieChartYear);
      await fetchExportPieChartWithYear(selectedExportPieChartYear);
    } catch (err) {
      console.error('Error fetching pie chart data:', err);
      setError('Failed to load pie chart data');
    }
  };

  // Fetch import company pie chart data with specific year
  const fetchImportCompanyPieChartWithYear = async (yearValue: string) => {
    try {
      console.log('IMPORT COMPANY PIE CHART DEBUG - Fetching import company pie chart with direct year value:', yearValue);
      console.log('IMPORT COMPANY PIE CHART DEBUG - Destination/Origin:', selectedCountry || 'All Destinations/Origins');
      console.log('IMPORT COMPANY PIE CHART DEBUG - Product:', selectedProduct || 'All Products');

      // Check if user has permission for import type
      if (session?.user?.role !== 'admin' &&
          Array.isArray(session?.user?.permissions?.types) &&
          !session?.user?.permissions?.types.includes('import')) {
        console.log('User does not have permission for import company data');
        setImportCompanyPieChartData(null);
        return;
      }

      // Create params object
      const params: any = {
        country: selectedCountry || undefined,
        fileCountry: selectedFileCountry || undefined,
        product: selectedProduct || undefined,
        type: 'import', // Always fetch import data only
        chartType: 'pie',
        groupBy: 'company',
      };

      // Always include year parameter, even if it's empty string (which means "All Years")
      // This ensures consistent behavior between selecting a specific year and "All Years"
      params.year = yearValue;

      console.log('IMPORT COMPANY PIE CHART DEBUG - Final params:', params);
      console.log('IMPORT COMPANY PIE CHART DEBUG - API URL:', '/api/charts');

      // Fetch import company pie chart data
      const importCompanyPieChartResponse = await axios.get('/api/charts', { params });

      console.log('IMPORT COMPANY PIE CHART DEBUG - Response status:', importCompanyPieChartResponse.status);
      console.log('IMPORT COMPANY PIE CHART DEBUG - Data received from API:', importCompanyPieChartResponse.data);

      // Only set the import data
      if (importCompanyPieChartResponse.data.import) {
        setImportCompanyPieChartData(importCompanyPieChartResponse.data.import);
      } else {
        setImportCompanyPieChartData(null);
      }
    } catch (err: any) {
      console.log('Error fetching import company pie chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for import company pie chart data');
        setImportCompanyPieChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load import company pie chart data');
      }
    }
  };

  // Fetch export company pie chart data with specific year
  const fetchExportCompanyPieChartWithYear = async (yearValue: string) => {
    try {
      console.log('EXPORT COMPANY PIE CHART DEBUG - Fetching export company pie chart with direct year value:', yearValue);
      console.log('EXPORT COMPANY PIE CHART DEBUG - Destination/Origin:', selectedCountry || 'All Destinations/Origins');
      console.log('EXPORT COMPANY PIE CHART DEBUG - Product:', selectedProduct || 'All Products');

      // Check if user has permission for export type
      if (session?.user?.role !== 'admin' &&
          Array.isArray(session?.user?.permissions?.types) &&
          !session?.user?.permissions?.types.includes('export')) {
        console.log('User does not have permission for export company data');
        setExportCompanyPieChartData(null);
        return;
      }

      // Create params object
      const params: any = {
        country: selectedCountry || undefined,
        fileCountry: selectedFileCountry || undefined,
        product: selectedProduct || undefined,
        type: 'export', // Always fetch export data only
        chartType: 'pie',
        groupBy: 'company',
      };

      // Always include year parameter, even if it's empty string (which means "All Years")
      // This ensures consistent behavior between selecting a specific year and "All Years"
      params.year = yearValue;

      console.log('EXPORT COMPANY PIE CHART DEBUG - Final params:', params);
      console.log('EXPORT COMPANY PIE CHART DEBUG - API URL:', '/api/charts');

      // Fetch export company pie chart data
      const exportCompanyPieChartResponse = await axios.get('/api/charts', { params });

      console.log('EXPORT COMPANY PIE CHART DEBUG - Response status:', exportCompanyPieChartResponse.status);
      console.log('EXPORT COMPANY PIE CHART DEBUG - Data received from API:', exportCompanyPieChartResponse.data);

      // Only set the export data
      if (exportCompanyPieChartResponse.data.export) {
        setExportCompanyPieChartData(exportCompanyPieChartResponse.data.export);
      } else {
        setExportCompanyPieChartData(null);
      }
    } catch (err: any) {
      console.log('Error fetching export company pie chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for export company pie chart data');
        setExportCompanyPieChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load export company pie chart data');
      }
    }
  };

  // Fetch company pie chart data
  const fetchCompanyPieChart = async () => {
    try {
      // Fetch import and export company pie chart data with the current year values
      await fetchImportCompanyPieChartWithYear(selectedImportCompanyPieChartYear);
      await fetchExportCompanyPieChartWithYear(selectedExportCompanyPieChartYear);
    } catch (err) {
      console.error('Error fetching company pie chart data:', err);
      setError('Failed to load company pie chart data');
    }
  };

  // Fetch import buyer pie chart data with specific year
  const fetchImportBuyerPieChartWithYear = async (yearValue: string) => {
    try {
      console.log('IMPORT BUYER PIE CHART DEBUG - Fetching import buyer pie chart with direct year value:', yearValue);
      console.log('IMPORT BUYER PIE CHART DEBUG - Destination/Origin:', selectedCountry || 'All Destinations/Origins');
      console.log('IMPORT BUYER PIE CHART DEBUG - Product:', selectedProduct || 'All Products');

      // Check if user has permission for import type
      if (session?.user?.role !== 'admin' &&
          Array.isArray(session?.user?.permissions?.types) &&
          !session?.user?.permissions?.types.includes('import')) {
        console.log('User does not have permission for import buyer data');
        setImportBuyerPieChartData(null);
        return;
      }

      // Create params object
      const params: any = {
        country: selectedCountry || undefined,
        fileCountry: selectedFileCountry || undefined,
        product: selectedProduct || undefined,
        type: 'import', // Always fetch import data only
        chartType: 'pie',
        groupBy: 'buyer',
      };

      // Always include year parameter, even if it's empty string (which means "All Years")
      // This ensures consistent behavior between selecting a specific year and "All Years"
      params.year = yearValue;

      console.log('IMPORT BUYER PIE CHART DEBUG - Final params:', params);
      console.log('IMPORT BUYER PIE CHART DEBUG - API URL:', '/api/charts');

      // Fetch import buyer pie chart data
      const importBuyerPieChartResponse = await axios.get('/api/charts', { params });

      console.log('IMPORT BUYER PIE CHART DEBUG - Response status:', importBuyerPieChartResponse.status);
      console.log('IMPORT BUYER PIE CHART DEBUG - Data received from API:', importBuyerPieChartResponse.data);

      // Only set the import data
      if (importBuyerPieChartResponse.data.import) {
        setImportBuyerPieChartData(importBuyerPieChartResponse.data.import);
      } else {
        setImportBuyerPieChartData(null);
      }
    } catch (err: any) {
      console.log('Error fetching import buyer pie chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for import buyer pie chart data');
        setImportBuyerPieChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load import buyer pie chart data');
      }
    }
  };

  // Fetch export buyer pie chart data with specific year
  const fetchExportBuyerPieChartWithYear = async (yearValue: string) => {
    try {
      console.log('EXPORT BUYER PIE CHART DEBUG - Fetching export buyer pie chart with direct year value:', yearValue);
      console.log('EXPORT BUYER PIE CHART DEBUG - Destination/Origin:', selectedCountry || 'All Destinations/Origins');
      console.log('EXPORT BUYER PIE CHART DEBUG - Product:', selectedProduct || 'All Products');

      // Check if user has permission for export type
      if (session?.user?.role !== 'admin' &&
          Array.isArray(session?.user?.permissions?.types) &&
          !session?.user?.permissions?.types.includes('export')) {
        console.log('User does not have permission for export buyer data');
        setExportBuyerPieChartData(null);
        return;
      }

      // Create params object
      const params: any = {
        country: selectedCountry || undefined,
        fileCountry: selectedFileCountry || undefined,
        product: selectedProduct || undefined,
        type: 'export', // Always fetch export data only
        chartType: 'pie',
        groupBy: 'buyer',
      };

      // Always include year parameter, even if it's empty string (which means "All Years")
      // This ensures consistent behavior between selecting a specific year and "All Years"
      params.year = yearValue;

      console.log('EXPORT BUYER PIE CHART DEBUG - Final params:', params);
      console.log('EXPORT BUYER PIE CHART DEBUG - API URL:', '/api/charts');

      // Fetch export buyer pie chart data
      const exportBuyerPieChartResponse = await axios.get('/api/charts', { params });

      console.log('EXPORT BUYER PIE CHART DEBUG - Response status:', exportBuyerPieChartResponse.status);
      console.log('EXPORT BUYER PIE CHART DEBUG - Data received from API:', exportBuyerPieChartResponse.data);

      // Only set the export data
      if (exportBuyerPieChartResponse.data.export) {
        setExportBuyerPieChartData(exportBuyerPieChartResponse.data.export);
      } else {
        setExportBuyerPieChartData(null);
      }
    } catch (err: any) {
      console.log('Error fetching export buyer pie chart data:', err.message);

      // Handle 403 Forbidden errors gracefully
      if (err.response && err.response.status === 403) {
        console.log('Permission denied for export buyer pie chart data');
        setExportBuyerPieChartData(null);
      } else {
        // For other errors, set the general error message
        setError('Failed to load export buyer pie chart data');
      }
    }
  };

  // Fetch buyer pie chart data
  const fetchBuyerPieChart = async () => {
    try {
      // Fetch import and export buyer pie chart data with the current year values
      await fetchImportBuyerPieChartWithYear(selectedImportBuyerPieChartYear);
      await fetchExportBuyerPieChartWithYear(selectedExportBuyerPieChartYear);
    } catch (err) {
      console.error('Error fetching buyer pie chart data:', err);
      setError('Failed to load buyer pie chart data');
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Trade Data Dashboard</h1>

      {/* Show permission mode info for non-admin users */}
      {session?.user?.role !== 'admin' && (
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                <strong>Permission Mode:</strong> Your access is restricted based on your assigned permissions.
                You can only view data that matches ALL of your permission criteria (countries, products, years, and types).
              </p>
              <p className="text-sm text-blue-700 mt-2">
                <strong>Note:</strong> If you don't have any permissions for a specific category (e.g., no year permissions),
                you won't be able to access any data. Please contact your administrator if you need access.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <SearchableSelect
            label="Product"
            options={[
              { value: '', label: 'All Products' },
              ...products.map(product => ({
                value: product,
                label: product,
                disabled: !checkPermission('products', product)
              })),
            ]}
            value={selectedProduct}
            onChange={(value) => setSelectedProduct(value)}
            onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('products', value, label)}
            placeholder="Search products..."
            fullWidth
          />
          <SearchableSelect
            label="Country"
            options={[
              { value: '', label: 'All Countries' },
              ...fileCountries.map(country => ({
                value: country,
                label: country.charAt(0).toUpperCase() + country.slice(1).toLowerCase(),
                disabled: !checkPermission('fileCountries', country)
              })),
            ]}
            value={selectedFileCountry}
            onChange={(value) => setSelectedFileCountry(value)}
            onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('fileCountries', value, label)}
            placeholder="Search countries..."
            fullWidth
          />
          <SearchableSelect
            label="Type"
            options={[
              { value: '', label: 'All Types' },
              {
                value: 'import',
                label: 'Import',
                disabled: !checkPermission('types', 'import')
              },
              {
                value: 'export',
                label: 'Export',
                disabled: !checkPermission('types', 'export')
              },
            ]}
            value={selectedType}
            onChange={(value) => setSelectedType(value)}
            onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('types', value, label)}
            placeholder="Search types..."
            fullWidth
          />
          <SearchableSelect
            label={destinationOriginLabel}
            options={[
              { value: '', label: `All ${destinationOriginLabel}s` },
              ...countries.map(country => ({
                value: country,
                label: country.charAt(0).toUpperCase() + country.slice(1).toLowerCase(),
                disabled: !checkPermission('countries', country)
              })),
            ]}
            value={selectedCountry}
            onChange={(value) => setSelectedCountry(value)}
            onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('countries', value, label)}
            placeholder={`Search ${destinationOriginLabel.toLowerCase()}s...`}
            fullWidth
          />
        </div>
        <div className="mt-4 flex justify-end space-x-4">
          <Button
            variant="primary"
            onClick={fetchData}
            isLoading={isLoading}
          >
            Apply Filters
          </Button>
          <Button
            variant="secondary"
            onClick={handleDownload}
            disabled={isLoading || !Array.isArray(tableData) || tableData.length === 0}
          >
            Download Excel
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 gap-6">
        {/* Line Charts - Monthly and Yearly side by side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Month Line Chart */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="mb-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">{generateDynamicTitle('Monthly Quantity Trends')}</h2>
              </div>
              <div className="mt-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Years
                </label>
                <div className="flex flex-wrap gap-2">
                  {years.map(year => {
                    const yearStr = year.toString();
                    const hasPermission = checkPermission('years', yearStr);
                    return (
                    <button
                      key={year}
                      className={`px-2 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
                        selectedMonthChartYears.includes(yearStr)
                          ? 'bg-blue-600 text-white shadow-md hover:bg-blue-700'
                          : hasPermission
                            ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      }`}
                      title={hasPermission ? '' : 'Please contact an administrator for access'}
                      onClick={() => {
                        if (!hasPermission) {
                          handleDisabledOptionSelected('years', yearStr, yearStr);
                          return;
                        }
                        // Create new years array
                        let newYears;

                        if (selectedMonthChartYears.includes(yearStr)) {
                          // Remove year if already selected
                          newYears = selectedMonthChartYears.filter(y => y !== yearStr);
                        } else {
                          // Add year if not selected
                          newYears = [...selectedMonthChartYears, yearStr];
                        }

                        // Log the change for debugging
                        console.log(`BUTTON CLICK DEBUG - Year ${yearStr} ${selectedMonthChartYears.includes(yearStr) ? 'removed from' : 'added to'} monthly chart selection`);
                        console.log('BUTTON CLICK DEBUG - New monthly chart years selection:', newYears);

                        // Update state with the new years array
                        setSelectedMonthChartYears(newYears);

                        // Create a new function to fetch data with the exact years we want
                        const fetchDataWithExactYears = async (yearsToFetch: string[]) => {
                          try {
                            console.log('FETCH DEBUG - Fetching with exact years:', yearsToFetch);

                            if (yearsToFetch.length === 0) {
                              console.log('FETCH DEBUG - No years to fetch, clearing data');
                              setMonthLineChartData(null);
                              return;
                            }

                            // Fetch month line chart data with the exact years
                            const response = await axios.get('/api/charts', {
                              params: {
                                country: selectedCountry || undefined,
                                fileCountry: selectedFileCountry || undefined,
                                product: selectedProduct || undefined,
                                type: selectedType || undefined,
                                years: yearsToFetch.join(','),
                                chartType: 'monthLine',
                              },
                            });

                            console.log('FETCH DEBUG - Data received:', response.data);
                            setMonthLineChartData(response.data);

                            // 保存原始数据用于表格显示
                            if (response.data && response.data.rawData) {
                              setMonthlyRawData(response.data.rawData);
                            } else {
                              setMonthlyRawData([]);
                            }
                          } catch (err) {
                            console.error('Error fetching monthly chart data:', err);
                            setError('Failed to load monthly chart data');
                          }
                        };

                        // If we have at least one year selected, fetch data
                        // Otherwise, clear the chart data
                        if (newYears.length > 0) {
                          fetchDataWithExactYears(newYears);
                        } else {
                          setMonthLineChartData(null);
                        }
                      }}
                    >
                      {year}
                    </button>
                  );})}
                </div>
              </div>
            </div>

            {monthLineChartData && monthLineChartData.datasets && monthLineChartData.datasets.length > 0 ? (
              <div>
                <LineChart data={monthLineChartData} />

                {/* 月度数据表格 */}
                {monthlyRawData && monthlyRawData.length > 0 && (
                  <div className="mt-6">
                    {(() => {
                      // 生成动态表格标题
                      const dynamicTitle = generateDynamicTitle('Monthly Quantity Data');

                      // 处理数据：按月份分组，年份作为列
                      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

                      // 获取所有年份并排序
                      const allYears = [...new Set(monthlyRawData.map(row => row.year))].sort();

                      // 按月份组织数据
                      const monthlyData = monthNames.map((monthName, monthIndex) => {
                        const monthNumber = monthIndex + 1;
                        const monthData = { month: monthName };

                        allYears.forEach(year => {
                          // 汇总该月该年的所有数量（不区分类型）
                          const monthYearData = monthlyRawData.filter(row =>
                            row.month === monthNumber && row.year === year
                          );

                          const totalQuantity = monthYearData.reduce((sum, row) =>
                            sum + (typeof row.quantity === 'number' ? row.quantity : 0), 0
                          );

                          monthData[year] = totalQuantity;
                        });

                        return monthData;
                      });

                      return (
                        <>
                          <h3 className="text-md font-medium mb-3">{dynamicTitle}</h3>
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200 text-sm">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                                  {allYears.map(year => (
                                    <th key={year} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                      {year}
                                    </th>
                                  ))}
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {monthlyData.map((row, index) => (
                                  <tr key={row.month} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{row.month}</td>
                                    {allYears.map(year => (
                                      <td key={year} className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                        {row[year] ? row[year].toLocaleString() : '0'}
                                      </td>
                                    ))}
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex justify-center items-center h-[360px] bg-gray-50 rounded border border-gray-200">
                <p className="text-gray-500">
                  {selectedMonthChartYears.length === 0
                    ? "Please select at least one year to display data"
                    : "No data available for the selected years"}
                </p>
              </div>
            )}
          </div>

          {/* Year Line Chart */}
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="mb-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">{generateDynamicTitle('Yearly Quantity Trends')}</h2>
              </div>
              <div className="mt-2">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select Years
                </label>
                <div className="flex flex-wrap gap-2">
                  {years.map(year => {
                    const yearStr = year.toString();
                    const hasPermission = checkPermission('years', yearStr);
                    return (
                    <button
                      key={year}
                      className={`px-2 py-1 text-xs font-medium rounded-md transition-all duration-200 ${
                        selectedYearChartYears.includes(yearStr)
                          ? 'bg-blue-600 text-white shadow-md hover:bg-blue-700'
                          : hasPermission
                            ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      }`}
                      title={hasPermission ? '' : 'Please contact an administrator for access'}
                      onClick={() => {
                        if (!hasPermission) {
                          handleDisabledOptionSelected('years', yearStr, yearStr);
                          return;
                        }
                        // Create new years array
                        let newYears;

                        if (selectedYearChartYears.includes(yearStr)) {
                          // Remove year if already selected
                          newYears = selectedYearChartYears.filter(y => y !== yearStr);
                        } else {
                          // Add year if not selected
                          newYears = [...selectedYearChartYears, yearStr];
                        }

                        // Log the change for debugging
                        console.log(`Year ${yearStr} ${selectedYearChartYears.includes(yearStr) ? 'removed from' : 'added to'} selection`);
                        console.log('New years selection:', newYears);

                        // Update state
                        setSelectedYearChartYears(newYears);

                        // If we have at least one year selected, fetch data with the new years directly
                        // Otherwise, clear the chart data
                        if (newYears.length > 0) {
                          // Use the newYears directly instead of relying on the state update
                          fetchYearlyChartWithYears(newYears);
                        } else {
                          setYearLineChartData(null);
                        }
                      }}
                    >
                      {year}
                    </button>
                  );})}
                </div>
              </div>
            </div>

            {yearLineChartData && yearLineChartData.datasets && yearLineChartData.datasets.length > 0 ? (
              <div>
                <LineChart data={yearLineChartData} />

                {/* 年度数据表格 */}
                {yearlyRawData && yearlyRawData.length > 0 && (
                  <div className="mt-6">
                    {(() => {
                      // 生成动态表格标题
                      const dynamicTitle = generateDynamicTitle('Yearly Quantity Data');

                      // 处理数据：按年份汇总，不区分类型
                      const yearlyData = [];
                      const allYears = [...new Set(yearlyRawData.map(row => row.year))].sort();

                      allYears.forEach(year => {
                        const yearData = yearlyRawData.filter(row => row.year === year);
                        const totalQuantity = yearData.reduce((sum, row) =>
                          sum + (typeof row.quantity === 'number' ? row.quantity : 0), 0
                        );

                        yearlyData.push({
                          year: year,
                          quantity: totalQuantity
                        });
                      });

                      return (
                        <>
                          <h3 className="text-md font-medium mb-3">{dynamicTitle}</h3>
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200 text-sm">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {yearlyData.map((row, index) => (
                                  <tr key={row.year} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{row.year}</td>
                                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                      {row.quantity.toLocaleString()}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex justify-center items-center h-[360px] bg-gray-50 rounded border border-gray-200">
                <p className="text-gray-500">
                  {selectedYearChartYears.length === 0
                    ? "Please select at least one year to display data"
                    : "No data available for the selected years"}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Product/Destination Distribution Charts */}
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{generateDynamicTitle('Product/Destination/Origin Distribution')}</h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Import Distribution */}
            {showImportCharts && (
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium">Import Distribution</h3>
                  <div className="w-40">
                    <SearchableSelect
                      label="Year"
                      options={[
                        { value: '', label: 'All Years' },
                        ...years.map(year => ({
                          value: year.toString(),
                          label: year.toString(),
                          disabled: !checkPermission('years', year.toString())
                        }))
                      ]}
                      value={selectedImportPieChartYear}
                      onChange={(value) => {
                        console.log('IMPORT PIE CHART YEAR DEBUG - Selected year changed to:', value);
                        setSelectedImportPieChartYear(value);
                        fetchImportPieChartWithYear(value);
                      }}
                      onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('years', value, label)}
                      placeholder="Select year..."
                    />
                  </div>
                </div>

                {importPieChartData && importPieChartData.data && importPieChartData.data.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-center">
                      {importPieChartData.year ?
                        importPieChartData.year === '' ?
                          'All Years' :
                          `Year: ${importPieChartData.year}`
                        : ''}
                    </h4>
                    <PieChart
                      data={{
                        labels: importPieChartData.labels,
                        datasets: [
                          {
                            data: importPieChartData.data,
                            backgroundColor: importPieChartData.backgroundColor,
                            borderWidth: 1,
                            borderColor: Array(importPieChartData.data.length).fill('#fff'),
                          },
                        ],
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex flex-col justify-center items-center h-[300px] bg-gray-50 rounded border border-gray-200">
                    <p className="text-gray-500 mb-2">No import data available</p>
                    <p className="text-gray-400 text-sm">You may not have permission to view import data</p>
                  </div>
                )}
              </div>
            )}

            {/* Export Distribution */}
            {showExportCharts && (
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium">Export Distribution</h3>
                  <div className="w-40">
                    <SearchableSelect
                      label="Year"
                      options={[
                        { value: '', label: 'All Years' },
                        ...years.map(year => ({
                          value: year.toString(),
                          label: year.toString(),
                          disabled: !checkPermission('years', year.toString())
                        }))
                      ]}
                      value={selectedExportPieChartYear}
                      onChange={(value) => {
                        console.log('EXPORT PIE CHART YEAR DEBUG - Selected year changed to:', value);
                        setSelectedExportPieChartYear(value);
                        fetchExportPieChartWithYear(value);
                      }}
                      onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('years', value, label)}
                      placeholder="Select year..."
                    />
                  </div>
                </div>

                {exportPieChartData && exportPieChartData.data && exportPieChartData.data.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-center">
                      {exportPieChartData.year ?
                        exportPieChartData.year === '' ?
                          'All Years' :
                          `Year: ${exportPieChartData.year}`
                        : ''}
                    </h4>
                    <PieChart
                      data={{
                        labels: exportPieChartData.labels,
                        datasets: [
                          {
                            data: exportPieChartData.data,
                            backgroundColor: exportPieChartData.backgroundColor,
                            borderWidth: 1,
                            borderColor: Array(exportPieChartData.data.length).fill('#fff'),
                          },
                        ],
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-[300px] bg-gray-50 rounded border border-gray-200">
                    <p className="text-gray-500">No export data available</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Company Distribution Charts */}
        <div className="bg-white p-4 rounded-lg shadow mt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{generateDynamicTitle('Top 5 Companies Distribution')}</h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Import Company Distribution */}
            {showImportCharts && (
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium">Top Importing Companies</h3>
                  <div className="w-40">
                    <SearchableSelect
                      label="Year"
                      options={[
                        { value: '', label: 'All Years' },
                        ...years.map(year => ({
                          value: year.toString(),
                          label: year.toString(),
                          disabled: !checkPermission('years', year.toString())
                        }))
                      ]}
                      value={selectedImportCompanyPieChartYear}
                      onChange={(value) => {
                        console.log('IMPORT COMPANY PIE CHART YEAR DEBUG - Selected year changed to:', value);
                        setSelectedImportCompanyPieChartYear(value);
                        fetchImportCompanyPieChartWithYear(value);
                      }}
                      onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('years', value, label)}
                      placeholder="Select year..."
                    />
                  </div>
                </div>

                {importCompanyPieChartData && importCompanyPieChartData.data && importCompanyPieChartData.data.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-center">
                      {importCompanyPieChartData.year ?
                        importCompanyPieChartData.year === '' ?
                          'All Years' :
                          `Year: ${importCompanyPieChartData.year}`
                        : ''}
                    </h4>
                    <PieChart
                      data={{
                        labels: importCompanyPieChartData.labels,
                        datasets: [
                          {
                            data: importCompanyPieChartData.data,
                            backgroundColor: importCompanyPieChartData.backgroundColor,
                            borderWidth: 1,
                            borderColor: Array(importCompanyPieChartData.data.length).fill('#fff'),
                          },
                        ],
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-[300px] bg-gray-50 rounded border border-gray-200">
                    <p className="text-gray-500">No import company data available</p>
                  </div>
                )}
              </div>
            )}

            {/* Export Company Distribution */}
            {showExportCharts && (
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium">Top Exporting Companies</h3>
                  <div className="w-40">
                    <SearchableSelect
                      label="Year"
                      options={[
                        { value: '', label: 'All Years' },
                        ...years.map(year => ({
                          value: year.toString(),
                          label: year.toString(),
                          disabled: !checkPermission('years', year.toString())
                        }))
                      ]}
                      value={selectedExportCompanyPieChartYear}
                      onChange={(value) => {
                        console.log('EXPORT COMPANY PIE CHART YEAR DEBUG - Selected year changed to:', value);
                        setSelectedExportCompanyPieChartYear(value);
                        fetchExportCompanyPieChartWithYear(value);
                      }}
                      onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('years', value, label)}
                      placeholder="Select year..."
                    />
                  </div>
                </div>

                {exportCompanyPieChartData && exportCompanyPieChartData.data && exportCompanyPieChartData.data.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-center">
                      {exportCompanyPieChartData.year ?
                        exportCompanyPieChartData.year === '' ?
                          'All Years' :
                          `Year: ${exportCompanyPieChartData.year}`
                        : ''}
                    </h4>
                    <PieChart
                      data={{
                        labels: exportCompanyPieChartData.labels,
                        datasets: [
                          {
                            data: exportCompanyPieChartData.data,
                            backgroundColor: exportCompanyPieChartData.backgroundColor,
                            borderWidth: 1,
                            borderColor: Array(exportCompanyPieChartData.data.length).fill('#fff'),
                          },
                        ],
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-[300px] bg-gray-50 rounded border border-gray-200">
                    <p className="text-gray-500">No export company data available</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Buyer Distribution Charts */}
        <div className="bg-white p-4 rounded-lg shadow mt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">{generateDynamicTitle('Buyer Distribution')}</h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Import Buyer Distribution */}
            {showImportCharts && (
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium">Import Buyers</h3>
                  <div className="w-40">
                    <SearchableSelect
                      label="Year"
                      options={[
                        { value: '', label: 'All Years' },
                        ...years.map(year => ({
                          value: year.toString(),
                          label: year.toString(),
                          disabled: !checkPermission('years', year.toString())
                        }))
                      ]}
                      value={selectedImportBuyerPieChartYear}
                      onChange={(value) => {
                        console.log('IMPORT BUYER PIE CHART YEAR DEBUG - Selected year changed to:', value);
                        setSelectedImportBuyerPieChartYear(value);
                        fetchImportBuyerPieChartWithYear(value);
                      }}
                      onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('years', value, label)}
                      placeholder="Select year..."
                    />
                  </div>
                </div>

                {importBuyerPieChartData && importBuyerPieChartData.data && importBuyerPieChartData.data.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-center">
                      {importBuyerPieChartData.year ?
                        importBuyerPieChartData.year === '' ?
                          'All Years' :
                          `Year: ${importBuyerPieChartData.year}`
                        : ''}
                    </h4>
                    <PieChart
                      data={{
                        labels: importBuyerPieChartData.labels,
                        datasets: [
                          {
                            data: importBuyerPieChartData.data,
                            backgroundColor: importBuyerPieChartData.backgroundColor,
                            borderWidth: 1,
                            borderColor: Array(importBuyerPieChartData.data.length).fill('#fff'),
                          },
                        ],
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-[300px] bg-gray-50 rounded border border-gray-200">
                    <p className="text-gray-500">No import buyer data available</p>
                  </div>
                )}
              </div>
            )}

            {/* Export Buyer Distribution */}
            {showExportCharts && (
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-md font-medium">Export Buyers</h3>
                  <div className="w-40">
                    <SearchableSelect
                      label="Year"
                      options={[
                        { value: '', label: 'All Years' },
                        ...years.map(year => ({
                          value: year.toString(),
                          label: year.toString(),
                          disabled: !checkPermission('years', year.toString())
                        }))
                      ]}
                      value={selectedExportBuyerPieChartYear}
                      onChange={(value) => {
                        console.log('EXPORT BUYER PIE CHART YEAR DEBUG - Selected year changed to:', value);
                        setSelectedExportBuyerPieChartYear(value);
                        fetchExportBuyerPieChartWithYear(value);
                      }}
                      onDisabledOptionSelected={(value, label) => handleDisabledOptionSelected('years', value, label)}
                      placeholder="Select year..."
                    />
                  </div>
                </div>

                {exportBuyerPieChartData && exportBuyerPieChartData.data && exportBuyerPieChartData.data.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2 text-center">
                      {exportBuyerPieChartData.year ?
                        exportBuyerPieChartData.year === '' ?
                          'All Years' :
                          `Year: ${exportBuyerPieChartData.year}`
                        : ''}
                    </h4>
                    <PieChart
                      data={{
                        labels: exportBuyerPieChartData.labels,
                        datasets: [
                          {
                            data: exportBuyerPieChartData.data,
                            backgroundColor: exportBuyerPieChartData.backgroundColor,
                            borderWidth: 1,
                            borderColor: Array(exportBuyerPieChartData.data.length).fill('#fff'),
                          },
                        ],
                      }}
                    />
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-[300px] bg-gray-50 rounded border border-gray-200">
                    <p className="text-gray-500">No export buyer data available</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Trade Data Table */}
        <div className="bg-white p-4 rounded-lg shadow mt-6">
          <h2 className="text-lg font-semibold mb-4">{generateDynamicTitle('Trade Data')}</h2>

          {/* 记录表格数据用于调试 */}
          {(() => { console.log('Trade Data Table - tableData:', tableData); return null; })()}
          {Array.isArray(tableData) && tableData.length > 0 ? (
            <div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    {(() => {
                      const headers = getDynamicHeaders();
                      return (
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quarter</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Month</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HS</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HS name</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exp/Imp</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{headers.company}</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{headers.port}</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{headers.destinationOrigin}</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Destination port</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit price (USD/kg)</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value (USD)</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                          {headers.showManufacturer && (
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacturer</th>
                          )}
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{headers.importerExporter}</th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                      );
                    })()}
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tableData.slice(0, visibleRows).map((row, index) => {
                      const headers = getDynamicHeaders();
                      // 计算季度
                      const quarter = row.month ? Math.ceil(row.month / 3) : '';

                      return (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.year || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{quarter}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.month || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.hsCode || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.hsName || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.type || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.company || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.exportPort || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {row.destination ? row.destination.charAt(0).toUpperCase() + row.destination.slice(1).toLowerCase() : ''}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.destinationPort || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {typeof row.quantity === 'number' ? row.quantity.toLocaleString() : row.quantity || ''}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.unit || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {typeof row.unitPrice === 'number' ? `$${row.unitPrice.toFixed(2)}` : row.unitPrice || ''}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {typeof row.value === 'number' ? `$${row.value.toLocaleString()}` : row.value || ''}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.product || ''}</td>
                          {headers.showManufacturer && (
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.manufacturer || ''}</td>
                          )}
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.buyer || ''}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{row.description || ''}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {Array.isArray(tableData) && tableData.length > 10 && visibleRows < tableData.length && (
                <div className="mt-4 flex justify-start">
                  <button
                    onClick={() => setVisibleRows(prev => prev + 10)}
                    className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Load More
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500">No data available. Please select filters and click "Apply Filters" to load data.</p>
              <p className="text-gray-400 text-sm mt-2">If you've applied filters but still don't see any data, please check your permissions or contact an administrator.</p>
            </div>
          )}
        </div>
      </div>

      {/* Permission Alert Dialog */}
      <Modal
        isOpen={showPermissionModal}
        onClose={() => setShowPermissionModal(false)}
        title="Permission Notice"
        footer={
          <Button variant="primary" onClick={() => setShowPermissionModal(false)}>
            OK
          </Button>
        }
      >
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
            <svg className="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <p className="text-sm text-gray-700 mb-4">{permissionModalMessage}</p>
        </div>
      </Modal>
    </div>
  );
};

export default DashboardClient;
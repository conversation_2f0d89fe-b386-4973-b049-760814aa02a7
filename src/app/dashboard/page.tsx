import React from 'react';
import DashboardClient from './DashboardClient';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import MainLayout from '@/components/layout/MainLayout';

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  return (
    <MainLayout>
      <DashboardClient />
    </MainLayout>
  );
}

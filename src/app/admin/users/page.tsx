'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import MainLayout from '@/components/layout/MainLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Select from '@/components/ui/Select';

interface User {
  _id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  permissions: {
    countries: string[];
    products: string[];
    years: string[];
    types: string[];
    fileCountries: string[];
    origins: string[];
    destinations: string[];
  };
  createdAt: string;
}

const UsersPage: React.FC = () => {
  const { data: session } = useSession();

  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState('user');

  const [countries, setCountries] = useState<string[]>([]);
  const [products, setProducts] = useState<string[]>([]);
  const [years, setYears] = useState<string[]>([]);
  const [types, setTypes] = useState<string[]>([]);
  const [fileCountries, setFileCountries] = useState<string[]>([]);
  const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedYears, setSelectedYears] = useState<string[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedFileCountries, setSelectedFileCountries] = useState<string[]>([]);

  // Fetch users
  const fetchUsers = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await axios.get('/api/users');

      // Log the users data for debugging
      console.log('Users data from API:', JSON.stringify(response.data.users.map((user: any) => ({
        _id: user._id,
        username: user.username,
        permissions: user.permissions
      })), null, 2));

      setUsers(response.data.users);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch metadata for permissions
  const fetchMetadata = async () => {
    try {
      const response = await axios.get('/api/metadata');

      // Ensure we have valid arrays for all metadata
      setCountries(Array.isArray(response.data.countries) ? response.data.countries : []);
      setProducts(Array.isArray(response.data.products) ? response.data.products : []);

      // Convert years to strings and ensure it's an array
      const yearsData = Array.isArray(response.data.years) ? response.data.years : [];
      setYears(yearsData.map(year => year.toString()));

      // Ensure types is an array
      setTypes(Array.isArray(response.data.types) ? response.data.types : []);

      // Ensure fileCountries is an array and filter out any null/empty values
      const fileCountriesData = Array.isArray(response.data.fileCountries) ? response.data.fileCountries : [];
      setFileCountries(fileCountriesData.filter(country => country && country.trim() !== ''));

      console.log('Metadata loaded:', {
        countries: response.data.countries?.length || 0,
        products: response.data.products?.length || 0,
        years: response.data.years?.length || 0,
        types: response.data.types?.length || 0,
        fileCountries: response.data.fileCountries?.length || 0,
        filteredFileCountries: fileCountriesData.filter(country => country && country.trim() !== '').length
      });
    } catch (err) {
      console.error('Error fetching metadata:', err);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchMetadata();
  }, []);

  // Reset form
  const resetForm = () => {
    setUsername('');
    setEmail('');
    setPassword('');
    setRole('user');
    setSelectedCountries([]);
    setSelectedProducts([]);
    setSelectedYears([]);
    setSelectedTypes([]);
    setSelectedFileCountries([]);
    setEditingUser(null);
    setShowAddForm(false);
  };

  // Handle create user
  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Log permissions before sending
      console.log('Selected years for create:', selectedYears);
      console.log('Selected types for create:', selectedTypes);

      const userData = {
        username,
        email,
        password,
        role,
        permissions: {
          countries: selectedCountries,
          products: selectedProducts,
          years: selectedYears,
          types: selectedTypes,
          fileCountries: selectedFileCountries,
        },
      };

      console.log('Creating user with permissions:', JSON.stringify(userData.permissions));

      const response = await axios.post('/api/users', userData);
      console.log('Create response:', response.data);

      resetForm();
      fetchUsers();
    } catch (err: any) {
      console.error('Error creating user:', err);
      setError(err.response?.data?.error || 'Failed to create user');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle edit user
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setUsername(user.username);
    setEmail(user.email);
    setPassword(''); // Don't set password for editing
    setRole(user.role);

    // Ensure all permission arrays are valid
    setSelectedCountries(Array.isArray(user.permissions.countries) ? user.permissions.countries : []);
    setSelectedProducts(Array.isArray(user.permissions.products) ? user.permissions.products : []);
    setSelectedYears(Array.isArray(user.permissions.years) ? user.permissions.years : []);
    setSelectedTypes(Array.isArray(user.permissions.types) ? user.permissions.types : []);

    // Ensure fileCountries is an array and filter out any null/empty values
    const fileCountriesData = Array.isArray(user.permissions.fileCountries) ? user.permissions.fileCountries : [];
    setSelectedFileCountries(fileCountriesData.filter(country => country && country.trim() !== ''));

    console.log('Editing user with permissions:', {
      countries: user.permissions.countries?.length || 0,
      products: user.permissions.products?.length || 0,
      years: user.permissions.years?.length || 0,
      types: user.permissions.types?.length || 0,
      fileCountries: user.permissions.fileCountries?.length || 0,
      filteredFileCountries: fileCountriesData.filter(country => country && country.trim() !== '').length
    });

    setShowAddForm(true);
  };

  // Handle update user
  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingUser) return;

    setIsLoading(true);
    setError('');

    try {
      // Log permissions before sending
      console.log('Selected years:', selectedYears);
      console.log('Selected types:', selectedTypes);

      const updateData: any = {
        username,
        email,
        role,
        permissions: {
          countries: selectedCountries,
          products: selectedProducts,
          years: selectedYears,
          types: selectedTypes,
          fileCountries: selectedFileCountries,
        },
      };

      console.log('Updating user with permissions:', JSON.stringify(updateData.permissions));

      // Only include password if it was changed
      if (password) {
        updateData.password = password;
      }

      const response = await axios.put(`/api/users/${editingUser._id}`, updateData);
      console.log('Update response:', response.data);

      resetForm();
      fetchUsers();
    } catch (err: any) {
      console.error('Error updating user:', err);
      setError(err.response?.data?.error || 'Failed to update user');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete user
  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) {
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      await axios.delete(`/api/users/${userId}`);
      fetchUsers();
    } catch (err: any) {
      console.error('Error deleting user:', err);
      setError(err.response?.data?.error || 'Failed to delete user');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle country selection
  const handleCountryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = e.target.options;
    const values = [];
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        values.push(options[i].value);
      }
    }
    setSelectedCountries(values);
  };

  // Handle product selection
  const handleProductChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = e.target.options;
    const values = [];
    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        values.push(options[i].value);
      }
    }
    setSelectedProducts(values);
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">User Management</h1>
          <Button
            variant="primary"
            onClick={() => {
              resetForm();
              setShowAddForm(!showAddForm);
            }}
          >
            {showAddForm ? 'Cancel' : 'Add User'}
          </Button>
        </div>

        {/* Permission mode explanation */}
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                <strong>Permission Mode:</strong> The system now uses an "AND" permission model. Users can only access data that matches ALL of their permission criteria simultaneously.
                For example, if a user has permissions for Country A, Product B, Year 2023, and Type "import", they can only view import data for Product B in Country A from 2023.
              </p>
              <p className="text-sm text-blue-700 mt-2">
                <strong>Empty Permissions:</strong> If a permission category (countries, products, years, or types) is set to empty (no selections),
                the user will not be able to access any data for that category. This effectively blocks all access to the data.
              </p>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Add/Edit User Form */}
        {showAddForm && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">
              {editingUser ? 'Edit User' : 'Add New User'}
            </h2>
            <form onSubmit={editingUser ? handleUpdateUser : handleCreateUser}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                  fullWidth
                />
                <Input
                  label="Email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  fullWidth
                />
                <Input
                  label={editingUser ? 'Password (leave blank to keep current)' : 'Password'}
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required={!editingUser}
                  fullWidth
                />
                <Select
                  label="Role"
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  options={[
                    { value: 'user', label: 'User' },
                    { value: 'admin', label: 'Admin' },
                  ]}
                  fullWidth
                />

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country Permissions (File Countries)
                  </label>
                  <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {fileCountries && fileCountries.length > 0 ? (
                        fileCountries.map((country) => (
                          country ? (
                            <div key={country} className="flex items-center">
                              <input
                                type="checkbox"
                                id={`filecountry-${country}`}
                                value={country}
                                checked={selectedFileCountries.includes(country)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedFileCountries([...selectedFileCountries, country]);
                                  } else {
                                    setSelectedFileCountries(selectedFileCountries.filter(c => c !== country));
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`filecountry-${country}`} className="ml-2 text-sm text-gray-700 truncate">
                                {country.charAt(0).toUpperCase() + country.slice(1).toLowerCase()}
                              </label>
                            </div>
                          ) : null
                        ))
                      ) : (
                        <div className="col-span-full text-sm text-gray-500">
                          No file countries available. Upload files to create country permissions.
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Destination/Origin Permissions
                  </label>
                  <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {countries.map((country) => (
                        <div key={country} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`country-${country}`}
                            value={country}
                            checked={selectedCountries.includes(country)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedCountries([...selectedCountries, country]);
                              } else {
                                setSelectedCountries(selectedCountries.filter(c => c !== country));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`country-${country}`} className="ml-2 text-sm text-gray-700 truncate">
                            {country.charAt(0).toUpperCase() + country.slice(1).toLowerCase()}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Product Permissions
                  </label>
                  <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                      {products.map((product) => (
                        <div key={product} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`product-${product}`}
                            value={product}
                            checked={selectedProducts.includes(product)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedProducts([...selectedProducts, product]);
                              } else {
                                setSelectedProducts(selectedProducts.filter(p => p !== product));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`product-${product}`} className="ml-2 text-sm text-gray-700 truncate">
                            {product}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Year Permissions
                  </label>
                  <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                      {years.map((year) => (
                        <div key={year} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`year-${year}`}
                            value={year}
                            checked={selectedYears.includes(year)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedYears([...selectedYears, year]);
                              } else {
                                setSelectedYears(selectedYears.filter(y => y !== year));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`year-${year}`} className="ml-2 text-sm text-gray-700">
                            {year}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type Permissions
                  </label>
                  <div className="border border-gray-300 rounded-md p-3">
                    <div className="grid grid-cols-2 gap-2">
                      {types.map((type) => (
                        <div key={type} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`type-${type}`}
                            value={type}
                            checked={selectedTypes.includes(type)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedTypes([...selectedTypes, type]);
                              } else {
                                setSelectedTypes(selectedTypes.filter(t => t !== type));
                              }
                            }}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`type-${type}`} className="ml-2 text-sm text-gray-700 capitalize">
                            {type}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end space-x-4">
                <Button
                  variant="secondary"
                  type="button"
                  onClick={resetForm}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  type="submit"
                  isLoading={isLoading}
                >
                  {editingUser ? 'Update User' : 'Create User'}
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Users Table */}
        <div className="bg-white overflow-x-auto rounded-lg shadow">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Countries</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Countries</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Years</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Types</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user._id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.username}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{user.email}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.permissions.countries.length > 0
                      ? `${user.permissions.countries.length} countries`
                      : 'None'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.permissions.fileCountries && user.permissions.fileCountries.length > 0
                      ? `${user.permissions.fileCountries.length} countries`
                      : 'None'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.permissions.products.length > 0
                      ? `${user.permissions.products.length} products`
                      : 'None'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.permissions.years && user.permissions.years.length > 0
                      ? `${user.permissions.years.length} years`
                      : 'None'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.permissions.types && user.permissions.types.length > 0
                      ? `${user.permissions.types.length} types`
                      : 'None'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEditUser(user)}
                      className="text-indigo-600 hover:text-indigo-900 mr-4"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteUser(user._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}

              {users.length === 0 && !isLoading && (
                <tr>
                  <td colSpan={10} className="px-6 py-4 text-center text-sm text-gray-500">
                    No users found
                  </td>
                </tr>
              )}

              {isLoading && (
                <tr>
                  <td colSpan={10} className="px-6 py-4 text-center text-sm text-gray-500">
                    Loading...
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </MainLayout>
  );
};

export default UsersPage;

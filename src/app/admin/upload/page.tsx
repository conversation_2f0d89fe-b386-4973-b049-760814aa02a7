'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import MainLayout from '@/components/layout/MainLayout';
import Button from '@/components/ui/Button';
import { useRouter } from 'next/navigation';

interface FileRecord {
  _id: string;
  fileName: string;
  originalName: string;
  size: number;
  countries: string[];
  products: string[];
  hsCodes?: string[];
  years: number[];
  dataCount: number;
  createdAt: string;
}

const UploadPage: React.FC = () => {
  const { data: session } = useSession();
  const router = useRouter();

  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [error, setError] = useState('');

  const [files, setFiles] = useState<FileRecord[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [deleteResult, setDeleteResult] = useState<{success: boolean; message: string} | null>(null);

  // Fetch uploaded files
  const fetchFiles = async () => {
    setIsLoadingFiles(true);

    try {
      const response = await axios.get('/api/files');
      setFiles(response.data.files);
    } catch (err) {
      console.error('Error fetching files:', err);
    } finally {
      setIsLoadingFiles(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, []);

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError('');
      setUploadResult(null);
    }
  };

  // Handle file upload
  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file to upload');
      return;
    }

    // Check file type
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      setError('Only Excel files (.xlsx, .xls) are allowed');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError('');
    setUploadResult(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || 1)
          );
          setUploadProgress(percentCompleted);
        },
      });

      setUploadResult(response.data);
      setFile(null);

      // Reset file input
      const fileInput = document.getElementById('file-upload') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }

      // Refresh file list
      fetchFiles();
    } catch (err: any) {
      console.error('Error uploading file:', err);
      setError(err.response?.data?.error || 'Failed to upload file');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle file deletion
  const handleDeleteFile = async (fileId: string) => {
    if (!confirm('确定要删除此文件吗？这将同时删除所有相关的数据记录，此操作不可撤销。')) {
      return;
    }

    setIsDeleting(fileId);
    setError('');
    setDeleteResult(null);

    try {
      const response = await axios.delete(`/api/files/${fileId}`);
      setDeleteResult({
        success: true,
        message: `文件 "${response.data.fileName}" 已成功删除`
      });

      // Refresh file list
      fetchFiles();
    } catch (err: any) {
      console.error('删除文件时出错:', err);
      setDeleteResult({
        success: false,
        message: err.response?.data?.error || '删除文件失败'
      });
    } finally {
      setIsDeleting(null);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Upload Trade Data</h1>

        {/* Upload Form */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Upload Excel File</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Excel File (.xlsx, .xls)
              </label>
              <input
                id="file-upload"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-semibold
                  file:bg-blue-50 file:text-blue-700
                  hover:file:bg-blue-100"
                disabled={isUploading}
              />
            </div>

            {file && (
              <div className="text-sm text-gray-500">
                Selected file: {file.name} ({formatFileSize(file.size)})
              </div>
            )}

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}

            {isUploading && (
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div
                  className="bg-blue-600 h-2.5 rounded-full"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
                <p className="text-sm text-gray-500 mt-1">
                  Uploading: {uploadProgress}%
                </p>
              </div>
            )}

            {uploadResult && (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <p>File uploaded successfully!</p>
                <p>Processed {uploadResult.dataCount} data records.</p>
              </div>
            )}

            <div className="flex justify-between">
              <a href="/trade-data-template.xlsx" download>
                <Button variant="secondary">
                  Download Template
                </Button>
              </a>
              <Button
                variant="primary"
                onClick={handleUpload}
                disabled={!file || isUploading}
                isLoading={isUploading}
              >
                Upload File
              </Button>
            </div>
          </div>
        </div>

        {/* Uploaded Files */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Uploaded Files</h2>

          {deleteResult && (
            <div className={`${deleteResult.success ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700'} border px-4 py-3 rounded mb-4`}>
              {deleteResult.message}
            </div>
          )}

          {isLoadingFiles ? (
            <p className="text-gray-500">Loading files...</p>
          ) : files.length === 0 ? (
            <p className="text-gray-500">No files uploaded yet.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Countries</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Products</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HS Codes</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Years</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Records</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uploaded At</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {files.map((file) => (
                    <tr key={file._id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.originalName}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.countries.length}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.products.length}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.hsCodes?.length || 0}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.years.join(', ')}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{file.dataCount}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(file.createdAt).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleDeleteFile(file._id)}
                          disabled={isDeleting === file._id}
                          className={`text-red-600 hover:text-red-900 ${isDeleting === file._id ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          {isDeleting === file._id ? '删除中...' : '删除'}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default UploadPage;

import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import User from '@/models/User';

// POST /api/auth/register - Register a new user
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { username, email, password } = body;

    // Validate required fields
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: 'Username, email, and password are required' },
        { status: 400 }
      );
    }

    // Validate password length
    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }],
    });

    if (existingUser) {
      if (existingUser.username === username) {
        return NextResponse.json(
          { error: 'Username already exists' },
          { status: 400 }
        );
      }
      if (existingUser.email === email) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 400 }
        );
      }
    }

    // Create default permissions for new user
    const defaultPermissions = {
      countries: [],
      products: [],
      years: [],
      types: [],
      fileCountries: [],
      origins: [],
      destinations: [],
    };

    // Create new user with default role and permissions
    const user = await User.create({
      username,
      email,
      password,
      role: 'user',
      permissions: defaultPermissions,
    });

    // Return success without sending password
    return NextResponse.json(
      { 
        success: true, 
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          role: user.role
        } 
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error registering user:', error);
    return NextResponse.json(
      { error: 'Failed to register user' },
      { status: 500 }
    );
  }
}

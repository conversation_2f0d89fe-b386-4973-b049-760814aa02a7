import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import File from '@/models/File';
import TradeData from '@/models/TradeData';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const unlinkAsync = promisify(fs.unlink);

// DELETE /api/files/[id] - Delete a file and its associated data (admin only)
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: '未授权：需要管理员权限' },
        { status: 403 }
      );
    }

    const { id } = params;

    await connectToDatabase();
    
    // Find file by ID
    const file = await File.findById(id);
    
    if (!file) {
      return NextResponse.json(
        { error: '文件未找到' },
        { status: 404 }
      );
    }
    
    // Delete associated trade data
    await TradeData.deleteMany({ fileName: file.fileName });
    
    // Delete physical file if it exists
    try {
      if (file.path && fs.existsSync(file.path)) {
        await unlinkAsync(file.path);
        console.log(`物理文件已删除: ${file.path}`);
      }
    } catch (fileError) {
      console.error(`删除物理文件时出错: ${fileError}`);
      // Continue with database deletion even if physical file deletion fails
    }
    
    // Delete file record from database
    await File.findByIdAndDelete(id);
    
    return NextResponse.json({ 
      message: '文件及相关数据已成功删除',
      fileName: file.originalName
    });
  } catch (error) {
    console.error('删除文件时出错:', error);
    return NextResponse.json(
      { error: '删除文件失败' },
      { status: 500 }
    );
  }
}

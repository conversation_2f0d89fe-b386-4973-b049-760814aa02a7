import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import TradeData from '@/models/TradeData';

// GET /api/metadata - Get available countries, products, and years
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    await connectToDatabase();

    // Build query for filtered data (used for products)
    const filteredQuery: any = {};

    // Build query for all data (used for countries and years)
    const allDataQuery: any = {};

    // Apply user permissions if not admin (only for products)
    if (session.user.role !== 'admin') {
      // Create an array to hold all permission conditions
      const permissionConditions = [];

      // Apply destination permissions
      if (session.user.permissions.countries.length > 0) {
        // If countries array has values, restrict to those countries
        permissionConditions.push({ destination: { $in: session.user.permissions.countries } });
      } else if (Array.isArray(session.user.permissions.countries) && session.user.permissions.countries.length === 0) {
        // If countries array is empty, deny access to all countries (impossible condition)
        permissionConditions.push({ destination: { $in: [] } });
      }

      // Apply product permissions
      if (session.user.permissions.products.length > 0) {
        // If products array has values, restrict to those products
        permissionConditions.push({ product: { $in: session.user.permissions.products } });
      } else if (Array.isArray(session.user.permissions.products) && session.user.permissions.products.length === 0) {
        // If products array is empty, deny access to all products (impossible condition)
        permissionConditions.push({ product: { $in: [] } });
      }

      // Apply year permissions
      if (session.user.permissions.years && session.user.permissions.years.length > 0) {
        // If years array has values, restrict to those years
        permissionConditions.push({ year: { $in: session.user.permissions.years.map(y => parseInt(y)) } });
      } else if (Array.isArray(session.user.permissions.years) && session.user.permissions.years.length === 0) {
        // If years array is empty, deny access to all years (impossible condition)
        permissionConditions.push({ year: { $in: [] } });
      }

      // Apply type permissions
      if (session.user.permissions.types && session.user.permissions.types.length > 0) {
        // If types array has values, restrict to those types
        permissionConditions.push({ type: { $in: session.user.permissions.types } });
      } else if (Array.isArray(session.user.permissions.types) && session.user.permissions.types.length === 0) {
        // If types array is empty, deny access to all types (impossible condition)
        permissionConditions.push({ type: { $in: [] } });
      }

      // Apply fileCountry permissions
      if (session.user.permissions.fileCountries && session.user.permissions.fileCountries.length > 0) {
        // If fileCountries array has values, restrict to those fileCountries
        permissionConditions.push({ fileCountry: { $in: session.user.permissions.fileCountries } });
      } else if (Array.isArray(session.user.permissions.fileCountries) && session.user.permissions.fileCountries.length === 0) {
        // If fileCountries array is empty, deny access to all fileCountries (impossible condition)
        permissionConditions.push({ fileCountry: { $in: [] } });
      }

      // If there are permission conditions, combine them with $and
      if (permissionConditions.length > 0) {
        filteredQuery.$and = permissionConditions;
      }

      console.log('Metadata API - Filtered query:', JSON.stringify(filteredQuery));
    }

    // Get all distinct destinations (countries) without filtering
    const allDestinations = await TradeData.distinct('destination', allDataQuery);

    // Get distinct products (filtered by permissions)
    const products = await TradeData.distinct('product', filteredQuery);

    // Get all distinct years without filtering
    const allYears = await TradeData.distinct('year', allDataQuery);

    // Get distinct HS codes (filtered by permissions)
    const hsCodes = await TradeData.distinct('hsCode', filteredQuery);

    // Get distinct companies (filtered by permissions)
    const companies = await TradeData.distinct('company', filteredQuery);

    // Get distinct types (import/export) - always return all types
    const types = await TradeData.distinct('type', {});

    // Get distinct fileCountries (filtered by permissions for non-admin users)
    let fileCountriesQuery = allDataQuery;
    if (session.user.role !== 'admin' && session.user.permissions.fileCountries && session.user.permissions.fileCountries.length > 0) {
      fileCountriesQuery = { ...fileCountriesQuery, fileCountry: { $in: session.user.permissions.fileCountries } };
    }

    // Get distinct fileCountries and filter out null/undefined values
    const rawFileCountries = await TradeData.distinct('fileCountry', fileCountriesQuery);
    const fileCountries = rawFileCountries.filter(country => country && country.trim() !== '');

    return NextResponse.json({
      countries: allDestinations.sort(),
      products: products.sort(),
      years: allYears.sort((a, b) => b - a), // Sort years in descending order
      hsCodes: hsCodes.sort(),
      companies: companies.sort(),
      types: types.sort(),
      fileCountries: fileCountries.sort(),
    });
  } catch (error) {
    console.error('Error fetching metadata:', error);
    return NextResponse.json(
      { error: 'Failed to fetch metadata' },
      { status: 500 }
    );
  }
}

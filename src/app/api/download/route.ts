import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import TradeData from '@/models/TradeData';
import * as XLSX from 'xlsx';

// GET /api/download - Download trade data as Excel
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const country = searchParams.get('country');
    const fileCountry = searchParams.get('fileCountry');
    const product = searchParams.get('product');
    const type = searchParams.get('type');
    const year = searchParams.get('year') ? parseInt(searchParams.get('year')!) : null;
    const hsCode = searchParams.get('hsCode');
    const company = searchParams.get('company');

    // Build query based on user permissions and filters
    const query: any = {};

    // Apply user permissions if not admin
    if (session.user.role !== 'admin') {
      // Create an array to hold all permission conditions
      const permissionConditions = [];

      // Check country permissions
      if (session.user.permissions.countries.length > 0) {
        if (country) {
          // If specific country requested, check if user has permission
          if (!session.user.permissions.countries.includes(country)) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this country' },
              { status: 403 }
            );
          }
          // Add the specific country to the query
          query.destination = country;
        } else {
          // If no specific country, add country permission condition
          permissionConditions.push({ destination: { $in: session.user.permissions.countries } });
        }
      } else if (Array.isArray(session.user.permissions.countries) && session.user.permissions.countries.length === 0) {
        // If countries array is empty, deny access to all countries
        if (country) {
          // If specific country requested but user has no country permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any country data' },
            { status: 403 }
          );
        } else {
          // If no specific country, add impossible condition to deny all countries
          permissionConditions.push({ destination: { $in: [] } });
        }
      }

      // Check product permissions
      if (session.user.permissions.products.length > 0) {
        if (product) {
          // If specific product requested, check if user has permission
          if (!session.user.permissions.products.includes(product)) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this product' },
              { status: 403 }
            );
          }
          // Add the specific product to the query
          query.product = product;
        } else {
          // If no specific product, add product permission condition
          permissionConditions.push({ product: { $in: session.user.permissions.products } });
        }
      } else if (Array.isArray(session.user.permissions.products) && session.user.permissions.products.length === 0) {
        // If products array is empty, deny access to all products
        if (product) {
          // If specific product requested but user has no product permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any product data' },
            { status: 403 }
          );
        } else {
          // If no specific product, add impossible condition to deny all products
          permissionConditions.push({ product: { $in: [] } });
        }
      }

      // Check year permissions
      if (session.user.permissions.years && session.user.permissions.years.length > 0) {
        if (year) {
          // If specific year requested, check if user has permission
          if (!session.user.permissions.years.includes(year.toString())) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this year' },
              { status: 403 }
            );
          }
          // Year is already added to the query below
        } else {
          // If no specific year, add year permission condition
          permissionConditions.push({ year: { $in: session.user.permissions.years.map(y => parseInt(y)) } });
        }
      } else if (Array.isArray(session.user.permissions.years) && session.user.permissions.years.length === 0) {
        // If years array is empty, deny access to all years
        if (year) {
          // If specific year requested but user has no year permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any year data' },
            { status: 403 }
          );
        } else {
          // If no specific year, add impossible condition to deny all years
          permissionConditions.push({ year: { $in: [] } });
        }
      }

      // Check type permissions
      if (session.user.permissions.types && session.user.permissions.types.length > 0) {
        if (type) {
          // If specific type requested, check if user has permission
          if (!session.user.permissions.types.includes(type)) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this type' },
              { status: 403 }
            );
          }
          // Type is already added to the query below
        } else {
          // If no specific type, add type permission condition
          permissionConditions.push({ type: { $in: session.user.permissions.types } });
        }
      } else if (Array.isArray(session.user.permissions.types) && session.user.permissions.types.length === 0) {
        // If types array is empty, deny access to all types
        if (type) {
          // If specific type requested but user has no type permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any type data' },
            { status: 403 }
          );
        } else {
          // If no specific type, add impossible condition to deny all types
          permissionConditions.push({ type: { $in: [] } });
        }
      }

      // If there are permission conditions, combine them with $and
      if (permissionConditions.length > 0) {
        // If query already has conditions, add them to permissionConditions
        const existingConditions = Object.entries(query).map(([key, value]) => ({ [key]: value }));

        if (existingConditions.length > 0) {
          query.$and = [...permissionConditions, ...existingConditions];

          // Remove the original conditions as they're now in $and
          for (const key of Object.keys(query)) {
            if (key !== '$and') {
              delete query[key];
            }
          }
        } else {
          query.$and = permissionConditions;
        }
      }

      console.log('Download API - Permission query:', JSON.stringify(query));
    } else {
      // Admin can access all data, just apply filters
      if (country) query.destination = country;
      if (product) query.product = product;
    }

    // Apply common filters
    if (type) query.type = type;
    if (year) query.year = year;
    if (hsCode) query.hsCode = hsCode;
    if (company) query.company = company;

    // Apply fileCountry filter if provided
    if (fileCountry) {
      // If type is specified, use fileCountry as origin for imports and destination for exports
      if (type === 'import') {
        // For import data, fileCountry is the origin country
        query.fileCountry = fileCountry;
      } else if (type === 'export') {
        // For export data, fileCountry is the destination country
        query.fileCountry = fileCountry;
      } else {
        // If type is not specified, match fileCountry regardless of type
        query.fileCountry = fileCountry;
      }
    }

    await connectToDatabase();

    // Get data
    const data = await TradeData.find(query)
      .sort({ year: -1, month: -1 })
      .select('-_id -__v -uploadedBy -fileName -createdAt -updatedAt');

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'No data found matching the criteria' },
        { status: 404 }
      );
    }

    // Convert to Excel
    const worksheet = XLSX.utils.json_to_sheet(data.map(item => item.toObject()));
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Trade Data');

    // Generate Excel buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Create filename
    const filename = `trade_data_${new Date().toISOString().split('T')[0]}.xlsx`;

    // Return Excel file
    return new NextResponse(excelBuffer, {
      headers: {
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      },
    });
  } catch (error) {
    console.error('Error downloading trade data:', error);
    return NextResponse.json(
      { error: 'Failed to download trade data' },
      { status: 500 }
    );
  }
}

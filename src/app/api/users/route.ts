import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import User from '@/models/User';

// GET /api/users - Get all users (admin only)
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized: Admin access required' },
        { status: 403 }
      );
    }

    await connectToDatabase();

    // Get all users but exclude password field
    const users = await User.find({}).select('-password');

    console.log('Users data from DB:', JSON.stringify(users.map(user => ({
      _id: user._id,
      username: user.username,
      permissions: user.permissions
    })), null, 2));

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user (admin only)
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized: Admin access required' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { username, email, password, role, permissions } = body;

    // Validate required fields
    if (!username || !email || !password) {
      return NextResponse.json(
        { error: 'Username, email, and password are required' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { email }],
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this username or email already exists' },
        { status: 400 }
      );
    }

    // Ensure permissions object has all required fields
    const userPermissions = permissions || {
      countries: [],
      products: [],
      years: [],
      types: [],
      fileCountries: [],
      origins: [],
      destinations: []
    };

    // Make sure all permission arrays exist
    userPermissions.countries = userPermissions.countries || [];
    userPermissions.products = userPermissions.products || [];
    userPermissions.years = userPermissions.years || [];
    userPermissions.types = userPermissions.types || [];
    userPermissions.fileCountries = userPermissions.fileCountries || [];
    userPermissions.origins = userPermissions.origins || [];
    userPermissions.destinations = userPermissions.destinations || [];

    console.log('Creating user with permissions:', JSON.stringify(userPermissions));

    // Create new user
    const user = await User.create({
      username,
      email,
      password,
      role: role || 'user',
      permissions: userPermissions,
    });

    // Return user without password
    return NextResponse.json(
      { user: { ...user.toObject(), password: undefined } },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}

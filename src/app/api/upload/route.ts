import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import File from '@/models/File';
import TradeData from '@/models/TradeData';
import * as XLSX from 'xlsx';
import { writeFile } from 'fs/promises';
import path from 'path';
import { mkdir } from 'fs/promises';

// Helper function to ensure upload directory exists
async function ensureUploadDir() {
  // Use environment variable or default to 'uploads' in current directory
  const uploadDirPath = process.env.UPLOAD_DIR || 'uploads';

  // Create absolute path
  const uploadDir = path.isAbsolute(uploadDirPath)
    ? uploadDirPath
    : path.join(process.cwd(), uploadDirPath);

  console.log(`Using upload directory: ${uploadDir}`);

  try {
    await mkdir(uploadDir, { recursive: true });

    // Check directory permissions
    const stats = require('fs').statSync(uploadDir);
    console.log(`Upload directory permissions: ${stats.mode.toString(8)}`);

    // Ensure directory is writable
    const testFile = path.join(uploadDir, '.test-write');
    await writeFile(testFile, 'test');
    await require('fs').promises.unlink(testFile);
    console.log('Upload directory is writable');

    return uploadDir;
  } catch (error) {
    console.error('Error with upload directory:', error);
    throw error;
  }
}

// POST /api/upload - Upload Excel file and process data (admin only)
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and is an admin
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized: Admin access required' },
        { status: 403 }
      );
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as unknown as {
      name: string;
      type: string;
      size: number;
      arrayBuffer: () => Promise<ArrayBuffer>;
    };

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      );
    }

    // Check file type
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      return NextResponse.json(
        { error: 'Only Excel files (.xlsx, .xls) are allowed' },
        { status: 400 }
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = await ensureUploadDir();

    // Generate unique filename
    const timestamp = Date.now();
    const fileName = `${timestamp}-${file.name}`;
    const filePath = path.join(uploadDir, fileName);

    // Extract country name from original file name
    // Remove file extension and any timestamp prefix
    const originalNameWithoutExt = file.name.replace(/\.(xlsx|xls)$/i, '');
    // Use the first part of the filename as the country name
    // Split by common separators and take the first part
    let fileCountry = originalNameWithoutExt.split(/[-_\s.]/)[0].toLowerCase();

    // Ensure fileCountry is a valid string
    fileCountry = fileCountry ? fileCountry.trim() : '';

    console.log(`Extracted country from filename: "${fileCountry}" from "${file.name}"`);

    // Validate that we have a non-empty country name
    if (!fileCountry) {
      return NextResponse.json(
        { error: 'Could not extract country name from file name. Please ensure the file name starts with a country name (e.g., china.xlsx).' },
        { status: 400 }
      );
    }

    // Convert file to buffer and save to disk
    console.log(`Saving file to: ${filePath}`);
    const buffer = Buffer.from(await file.arrayBuffer());

    try {
      // Ensure directory exists
      const dirPath = path.dirname(filePath);
      await mkdir(dirPath, { recursive: true });

      // Write file with explicit permissions
      await writeFile(filePath, buffer, { mode: 0o644 });
      console.log(`File saved successfully with size: ${buffer.length} bytes`);
    } catch (writeError) {
      console.error(`Error writing file: ${writeError.message}`);
      throw writeError;
    }

    // Connect to database
    await connectToDatabase();

    // Read Excel file directly from the buffer instead of from disk
    console.log('Processing Excel data from buffer...');
    let data;
    try {
      // Use the buffer we already have instead of reading from disk
      const workbook = XLSX.read(buffer);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      data = XLSX.utils.sheet_to_json(worksheet);
      console.log(`Successfully parsed Excel data: ${data.length} rows found`);

      // Log the first few rows to see the structure
      if (data.length > 0) {
        console.log('===== EXCEL DATA ANALYSIS =====');
        console.log(`Total rows: ${data.length}`);

        // Print the first 3 rows (or all if less than 3)
        const rowsToPrint = Math.min(3, data.length);
        for (let i = 0; i < rowsToPrint; i++) {
          console.log(`\n----- ROW ${i+1} -----`);
          console.log(`Full row data: ${JSON.stringify(data[i], null, 2)}`);
          console.log(`Row keys: ${Object.keys(data[i])}`);

          // Print all fields and their values
          const row = data[i];
          Object.keys(row).forEach(key => {
            console.log(`Field "${key}": ${row[key]} (type: ${typeof row[key]})`);
          });
        }

        // Check for unit price related fields in all rows
        console.log('\n----- UNIT PRICE FIELD ANALYSIS -----');
        const allFieldNames = new Set();
        const unitPriceFieldNames = new Set();

        // Collect all field names from all rows
        data.forEach(row => {
          Object.keys(row).forEach(key => {
            allFieldNames.add(key);
            if (
              key.toLowerCase().includes('price') ||
              key.toLowerCase().includes('unit') ||
              key.toLowerCase().includes('kg')
            ) {
              unitPriceFieldNames.add(key);
            }
          });
        });

        console.log(`All field names found in the Excel file: ${Array.from(allFieldNames)}`);
        console.log(`Potential unit price fields: ${Array.from(unitPriceFieldNames)}`);

        // Check values for potential unit price fields in first few rows
        Array.from(unitPriceFieldNames).forEach(field => {
          console.log(`\nValues for field "${field}" in first ${rowsToPrint} rows:`);
          for (let i = 0; i < rowsToPrint; i++) {
            const row = data[i];
            if (field in row) {
              console.log(`  Row ${i+1}: ${row[field]} (type: ${typeof row[field]})`);
            } else {
              console.log(`  Row ${i+1}: field not present`);
            }
          }
        });

        console.log('===== END OF EXCEL DATA ANALYSIS =====\n');
      }
    } catch (readError) {
      console.error(`Error reading Excel data: ${readError.message}`);
      throw new Error(`Failed to process Excel data: ${readError.message}`);
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Excel file is empty or has invalid format' },
        { status: 400 }
      );
    }

    // Extract unique destinations, products, and years from data
    const destinations = new Set<string>();
    const products = new Set<string>();
    const years = new Set<number>();
    const hsCodes = new Set<string>();

    // Process and save trade data
    const tradeData = [];

    for (const row of data) {
      console.log('Processing row:', JSON.stringify(row));

      // Extract date information
      let year, month, day;
      let date;

      // Try to parse the date from the Month field
      if (row.Month) {
        try {
          // Try different date formats
          if (typeof row.Month === 'string') {
            // Format: YYYY/MM/DD or YYYY-MM-DD
            const dateParts = row.Month.split(/[\/\-]/);
            if (dateParts.length >= 3) {
              year = parseInt(dateParts[0]);
              month = parseInt(dateParts[1]);
              day = parseInt(dateParts[2]);
              date = new Date(year, month - 1, day);
            }
          } else if (typeof row.Month === 'number') {
            // Excel serial date
            date = new Date(Math.round((row.Month - 25569) * 86400 * 1000));
            year = date.getFullYear();
            month = date.getMonth() + 1;
          } else {
            // Try to convert to date directly
            date = new Date(row.Month);
            year = date.getFullYear();
            month = date.getMonth() + 1;
          }
        } catch (e) {
          console.error('Error parsing date:', e);
        }
      }

      // Validate required fields
      if (!row.Destination || !row.Product || !row['Exp/Imp'] || !year || !month || !row.Quantity || !row.Unit) {
        console.warn('Skipping invalid row:', JSON.stringify(row));
        continue; // Skip invalid rows
      }

      // Add to sets for metadata
      destinations.add(row.Destination);
      products.add(row.Product);
      years.add(year);
      if (row.HS) {
        hsCodes.add(row.HS);
      }

      // Determine trade type
      const tradeType = row['Exp/Imp'].toLowerCase().includes('exp') ? 'export' : 'import';

      // Parse value and unit price
      let value = 0;
      let unitPrice = 0;

      console.log('Row value fields:', {
        value: row['Value (USD)'],
        unitPrice: row['Unit price (USD/kg)'],
        unitPriceType: typeof row['Unit price (USD/kg)']
      });

      // Try to find the unit price field with different possible formats
      // First, log all keys to see what's available
      console.log('All row keys:', Object.keys(row));

      // Try to find the unit price field with different possible formats
      const unitPriceField =
        row['Unit price (USD/kg)'] !== undefined ? 'Unit price (USD/kg)' :
        row[' Unit price (USD/kg)'] !== undefined ? ' Unit price (USD/kg)' :  // Space at the beginning
        row[' Unit price (USD/kg) '] !== undefined ? ' Unit price (USD/kg) ' :  // Space at beginning and end
        row['Unit price (USD/kg) '] !== undefined ? 'Unit price (USD/kg) ' :  // Space at the end
        row['Unit price'] !== undefined ? 'Unit price' :
        row['UnitPrice'] !== undefined ? 'UnitPrice' :
        row['Unit_price'] !== undefined ? 'Unit_price' :
        row['unit price'] !== undefined ? 'unit price' :
        row['unitprice'] !== undefined ? 'unitprice' : null;

      // Log the exact field name if found
      if (unitPriceField) {
        console.log(`Found unit price field with exact name: "${unitPriceField}"`);
      }

      // If we still can't find the unit price field, try a more aggressive approach
      // by searching through all keys
      let foundUnitPriceField = unitPriceField;
      if (!foundUnitPriceField) {
        const allKeys = Object.keys(row);
        for (const key of allKeys) {
          if (
            key.toLowerCase().includes('price') &&
            (key.toLowerCase().includes('unit') || key.toLowerCase().includes('kg'))
          ) {
            foundUnitPriceField = key;
            console.log(`Found potential unit price field by search: ${key}`);
            break;
          }
        }
      }

      if (row['Value (USD)'] !== undefined && row['Value (USD)'] !== null) {
        value = typeof row['Value (USD)'] === 'string'
          ? parseFloat(row['Value (USD)'].replace(/[^0-9.-]+/g, ''))
          : parseFloat(row['Value (USD)']);
      }

      // Special handling for the field with spaces
      // Check specifically for the field with spaces that we found in the data
      if (row[' Unit price (USD/kg) '] !== undefined && row[' Unit price (USD/kg) '] !== null) {
        console.log(`Found unit price in field with spaces: " Unit price (USD/kg) ", value: ${row[' Unit price (USD/kg) ']}`);

        // Handle different formats of unit price
        if (typeof row[' Unit price (USD/kg) '] === 'string') {
          // Remove any non-numeric characters except decimal point and negative sign
          const cleanedValue = row[' Unit price (USD/kg) '].replace(/[^0-9.-]+/g, '');
          unitPrice = cleanedValue ? parseFloat(cleanedValue) : 0;
        } else if (typeof row[' Unit price (USD/kg) '] === 'number') {
          unitPrice = row[' Unit price (USD/kg) '];
        }

        console.log(`Parsed unit price from field with spaces: ${unitPrice}`);
      }
      // Try with the found field if we haven't already set the unit price
      else if (foundUnitPriceField && row[foundUnitPriceField] !== undefined && row[foundUnitPriceField] !== null) {
        console.log(`Found unit price in field: ${foundUnitPriceField}, value: ${row[foundUnitPriceField]}`);

        // Handle different formats of unit price
        if (typeof row[foundUnitPriceField] === 'string') {
          // Remove any non-numeric characters except decimal point and negative sign
          const cleanedValue = row[foundUnitPriceField].replace(/[^0-9.-]+/g, '');
          unitPrice = cleanedValue ? parseFloat(cleanedValue) : 0;
        } else if (typeof row[foundUnitPriceField] === 'number') {
          unitPrice = row[foundUnitPriceField];
        }

        console.log(`Parsed unit price: ${unitPrice}`);
      } else {
        console.log('Unit price field not found or is null/undefined');

        // As a last resort, try to extract from the description or other fields
        if (row.Description && typeof row.Description === 'string') {
          const description = row.Description.toLowerCase();
          const priceMatch = description.match(/price\s*:\s*(\d+(\.\d+)?)/i) ||
                            description.match(/\$\s*(\d+(\.\d+)?)/i) ||
                            description.match(/(\d+(\.\d+)?)\s*\/\s*kg/i);

          if (priceMatch && priceMatch[1]) {
            unitPrice = parseFloat(priceMatch[1]);
            console.log(`Extracted unit price from description: ${unitPrice}`);
          }
        }
      }

      // Automatic unit price calculation is disabled
      // if (unitPrice === 0 && value > 0 && row.Quantity && parseFloat(row.Quantity) > 0) {
      //   unitPrice = value / parseFloat(row.Quantity);
      //   console.log('Calculated unit price from value and quantity:', unitPrice);
      // }

      // Log that automatic calculation is disabled
      if (unitPrice === 0 && value > 0 && row.Quantity && parseFloat(row.Quantity) > 0) {
        console.log('NOTE: Automatic unit price calculation is disabled. Unit price will remain 0.');
      }

      // If value is 0 but we have quantity and unit price, calculate value
      if (value === 0 && unitPrice > 0 && row.Quantity) {
        value = unitPrice * parseFloat(row.Quantity);
      }

      // Create trade data object
      const tradeDataObj = {
        // Date information
        date: date,
        year: year,
        month: month,

        // HS Code information
        hsCode: row.HS || '',
        hsName: row['HS name'] || '',

        // Trade information
        type: tradeType,
        company: row.Company || '',
        exportPort: row['Export port'] || '',
        destination: row.Destination,
        destinationPort: row['Destination port'] || '',
        fileCountry: fileCountry, // Add the country from filename

        // Product information
        product: row.Product,
        manufacturer: row.Manufacturer || '',
        buyer: row.Buyer || '',
        description: row.Description || '',

        // Quantity and value
        quantity: parseFloat(row.Quantity),
        unit: row.Unit,
        unitPrice: unitPrice,
        value: value,

        // System fields
        uploadedBy: session.user.id,
        fileName: fileName,
      };

      tradeData.push(tradeDataObj);
    }

    // Save data to database

    // Save file metadata
    const fileRecord = await File.create({
      fileName: fileName,
      originalName: file.name,
      path: filePath,
      size: buffer.length,
      mimetype: file.type,
      uploadedBy: session.user.id,
      countries: Array.from(destinations),
      products: Array.from(products),
      years: Array.from(years),
      hsCodes: Array.from(hsCodes),
      dataCount: tradeData.length,
    });

    // Save trade data in bulk
    if (tradeData.length > 0) {
      await TradeData.insertMany(tradeData);
    }

    return NextResponse.json({
      message: 'File uploaded and processed successfully',
      file: fileRecord,
      dataCount: tradeData.length,
    }, { status: 201 });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Failed to upload and process file' },
      { status: 500 }
    );
  }
}

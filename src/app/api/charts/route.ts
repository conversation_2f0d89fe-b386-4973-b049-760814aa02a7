import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import TradeData from '@/models/TradeData';

// GET /api/charts - Get chart data with filters
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized: Authentication required' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const country = searchParams.get('country');
    const fileCountry = searchParams.get('fileCountry');
    const product = searchParams.get('product');
    const type = searchParams.get('type');

    // Parse year parameter
    const yearParam = searchParams.get('year');
    console.log('API DEBUG - Raw year parameter:', yearParam);
    console.log('API DEBUG - Year parameter type:', typeof yearParam);

    // Only parse year if it's not an empty string (which means "All Years")
    // Make sure to handle the case where yearParam is a string representation of a number
    let year = null;
    if (yearParam && yearParam !== '') {
      // Try to parse as integer
      year = parseInt(yearParam);
      // Check if parsing was successful
      if (isNaN(year)) {
        console.log('API DEBUG - Failed to parse year parameter as integer, using raw value');
        year = yearParam;
      }
    }

    console.log('API DEBUG - Parsed year parameter:', year);
    console.log('API DEBUG - Parsed year parameter type:', typeof year);

    const yearsParam = searchParams.get('years');
    const years = yearsParam ? yearsParam.split(',').map(y => parseInt(y)) : [];
    const hsCode = searchParams.get('hsCode');
    const company = searchParams.get('company');
    const chartType = searchParams.get('chartType') || 'line'; // 'line', 'monthLine', 'yearLine', or 'pie'

    console.log('API DEBUG - All parameters:');
    console.log('API DEBUG - country:', country);
    console.log('API DEBUG - fileCountry:', fileCountry);
    console.log('API DEBUG - product:', product);
    console.log('API DEBUG - type:', type);
    console.log('API DEBUG - year:', year);
    console.log('API DEBUG - years:', years);
    console.log('API DEBUG - chartType:', chartType);
    console.log('API DEBUG - groupBy:', searchParams.get('groupBy'));

    // Build query based on user permissions and filters
    const query: any = {};

    // Apply user permissions if not admin
    if (session.user.role !== 'admin') {
      // Create an array to hold all permission conditions
      const permissionConditions = [];

      // Check country permissions
      if (session.user.permissions.countries.length > 0) {
        if (country) {
          // If specific country requested, check if user has permission
          if (!session.user.permissions.countries.includes(country)) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this country' },
              { status: 403 }
            );
          }
          // Add the specific country to the query
          query.destination = country;
        } else {
          // If no specific country, add country permission condition
          permissionConditions.push({ destination: { $in: session.user.permissions.countries } });
        }
      } else if (Array.isArray(session.user.permissions.countries) && session.user.permissions.countries.length === 0) {
        // If countries array is empty, deny access to all countries
        if (country) {
          // If specific country requested but user has no country permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any country data' },
            { status: 403 }
          );
        } else {
          // If no specific country, add impossible condition to deny all countries
          permissionConditions.push({ destination: { $in: [] } });
        }
      }

      // Check product permissions
      if (session.user.permissions.products.length > 0) {
        if (product) {
          // If specific product requested, check if user has permission
          if (!session.user.permissions.products.includes(product)) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this product' },
              { status: 403 }
            );
          }
          // Add the specific product to the query
          query.product = product;
        } else {
          // If no specific product, add product permission condition
          permissionConditions.push({ product: { $in: session.user.permissions.products } });
        }
      } else if (Array.isArray(session.user.permissions.products) && session.user.permissions.products.length === 0) {
        // If products array is empty, deny access to all products
        if (product) {
          // If specific product requested but user has no product permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any product data' },
            { status: 403 }
          );
        } else {
          // If no specific product, add impossible condition to deny all products
          permissionConditions.push({ product: { $in: [] } });
        }
      }

      // Check year permissions
      if (session.user.permissions.years && session.user.permissions.years.length > 0) {
        if (year) {
          // If specific year requested, check if user has permission
          if (!session.user.permissions.years.includes(year.toString())) {
            return NextResponse.json(
              { error: 'You do not have permission to access data for this year' },
              { status: 403 }
            );
          }
          // Year is already added to the query below
        } else if (years && years.length > 0) {
          // If specific years requested for year chart, check if user has permission for all
          const unauthorizedYears = years.filter(y =>
            !session.user.permissions.years.includes(y.toString())
          );

          if (unauthorizedYears.length > 0) {
            return NextResponse.json(
              { error: `You do not have permission to access data for years: ${unauthorizedYears.join(', ')}` },
              { status: 403 }
            );
          }
          // Years are already added to the query below
        } else {
          // If no specific year, add year permission condition
          permissionConditions.push({ year: { $in: session.user.permissions.years.map(y => parseInt(y)) } });
        }
      } else if (Array.isArray(session.user.permissions.years) && session.user.permissions.years.length === 0) {
        // If years array is empty, deny access to all years
        if (year) {
          // If specific year requested but user has no year permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any year data' },
            { status: 403 }
          );
        } else if (years && years.length > 0) {
          // If specific years requested for year chart but user has no year permissions
          return NextResponse.json(
            { error: 'You do not have permission to access any year data' },
            { status: 403 }
          );
        } else {
          // If no specific year, add impossible condition to deny all years
          permissionConditions.push({ year: { $in: [] } });
        }
      }

      // Check type permissions
      if (session.user.permissions.types && session.user.permissions.types.length > 0) {
        if (type) {
          // If specific type requested, check if user has permission
          if (!session.user.permissions.types.includes(type)) {
            console.log(`User ${session.user.email} does not have permission for type: ${type}`);
            console.log(`User's type permissions: ${session.user.permissions.types.join(', ')}`);

            // Return a more specific error message
            return NextResponse.json(
              {
                error: `You do not have permission to access ${type} data`,
                permittedTypes: session.user.permissions.types
              },
              { status: 403 }
            );
          }
          // Type is already added to the query below
        } else {
          // If no specific type, add type permission condition
          permissionConditions.push({ type: { $in: session.user.permissions.types } });
          console.log(`User ${session.user.email} has type permissions: ${session.user.permissions.types.join(', ')}`);
        }
      } else if (Array.isArray(session.user.permissions.types) && session.user.permissions.types.length === 0) {
        // If types array is empty, deny access to all types
        if (type) {
          // If specific type requested but user has no type permissions
          console.log(`User ${session.user.email} has no type permissions but requested type: ${type}`);

          return NextResponse.json(
            {
              error: 'You do not have permission to access any type data',
              permittedTypes: []
            },
            { status: 403 }
          );
        } else {
          // If no specific type, add impossible condition to deny all types
          console.log(`User ${session.user.email} has no type permissions, adding impossible condition`);
          permissionConditions.push({ type: { $in: [] } });
        }
      }

      // Check fileCountry permissions
      if (session.user.permissions.fileCountries && session.user.permissions.fileCountries.length > 0) {
        if (fileCountry) {
          // If specific fileCountry requested, check if user has permission
          if (!session.user.permissions.fileCountries.includes(fileCountry)) {
            console.log(`User ${session.user.email} does not have permission for fileCountry: ${fileCountry}`);
            console.log(`User's fileCountry permissions: ${session.user.permissions.fileCountries.join(', ')}`);

            // Return a more specific error message
            return NextResponse.json(
              {
                error: `You do not have permission to access data for country ${fileCountry}`,
                permittedFileCountries: session.user.permissions.fileCountries
              },
              { status: 403 }
            );
          }
          // fileCountry is added to the query below
        } else {
          // If no specific fileCountry, add fileCountry permission condition
          permissionConditions.push({ fileCountry: { $in: session.user.permissions.fileCountries } });
          console.log(`User ${session.user.email} has fileCountry permissions: ${session.user.permissions.fileCountries.join(', ')}`);
        }
      } else if (Array.isArray(session.user.permissions.fileCountries) && session.user.permissions.fileCountries.length === 0) {
        // If fileCountries array is empty, deny access to all fileCountries
        if (fileCountry) {
          // If specific fileCountry requested but user has no fileCountry permissions
          console.log(`User ${session.user.email} has no fileCountry permissions but requested fileCountry: ${fileCountry}`);

          return NextResponse.json(
            {
              error: 'You do not have permission to access any country data',
              permittedFileCountries: []
            },
            { status: 403 }
          );
        } else {
          // If no specific fileCountry, add impossible condition to deny all fileCountries
          console.log(`User ${session.user.email} has no fileCountry permissions, adding impossible condition`);
          permissionConditions.push({ fileCountry: { $in: [] } });
        }
      }

      // If there are permission conditions, combine them with $and
      if (permissionConditions.length > 0) {
        // If query already has conditions, add them to permissionConditions
        const existingConditions = Object.entries(query).map(([key, value]) => ({ [key]: value }));

        if (existingConditions.length > 0) {
          query.$and = [...permissionConditions, ...existingConditions];

          // Remove the original conditions as they're now in $and
          for (const key of Object.keys(query)) {
            if (key !== '$and') {
              delete query[key];
            }
          }
        } else {
          query.$and = permissionConditions;
        }
      }

      console.log('Charts API - Permission query:', JSON.stringify(query));
    } else {
      // Admin can access all data, just apply filters
      if (country) query.destination = country;
      if (product) query.product = product;
    }

    // Apply common filters
    if (type) query.type = type;

    // Only add year filter if year is not null (which means it's either a specific year or not provided at all)
    // If year is null but yearParam is an empty string, it means "All Years" was explicitly selected
    if (year !== null) {
      console.log('API DEBUG - Setting year filter:', year);
      query.year = year;
    } else if (yearParam === '') {
      console.log('API DEBUG - "All Years" explicitly selected, not filtering by year');
      // Do not add year filter, which means include all years
      // This is important for pie charts to show data from all years
      console.log('API DEBUG - Not adding year filter to query');
    } else {
      console.log('API DEBUG - No year parameter provided');
    }

    if (hsCode) query.hsCode = hsCode;
    if (company) query.company = company;

    // Apply fileCountry filter if provided
    if (fileCountry) {
      // If type is specified, use fileCountry as origin for imports and destination for exports
      if (type === 'import') {
        // For import data, fileCountry is the origin country
        query.fileCountry = fileCountry;
      } else if (type === 'export') {
        // For export data, fileCountry is the destination country
        query.fileCountry = fileCountry;
      } else {
        // If type is not specified, match fileCountry regardless of type
        query.fileCountry = fileCountry;
      }
    }

    console.log('API DEBUG - Final query:', JSON.stringify(query));

    await connectToDatabase();

    let chartData;

    if (chartType === 'line' || chartType === 'monthLine') {
      // For month line chart, check if years are specified
      if (years.length > 0) {
        query.year = { $in: years };
      }

      // For line chart, get detailed data for price analysis
      const detailedData = await TradeData.find(query)
        .select('year month type quantity unitPrice value')
        .sort({ year: 1, month: 1 })
        .lean();

      // Group by month and year for chart display
      chartData = await TradeData.aggregate([
        { $match: query },
        {
          $group: {
            _id: {
              year: '$year',
              month: '$month',
              type: '$type'
            },
            totalValue: { $sum: '$value' },
            totalQuantity: { $sum: '$quantity' }
          }
        },
        {
          $sort: {
            '_id.year': 1,
            '_id.month': 1
          }
        },
        {
          $project: {
            _id: 0,
            year: '$_id.year',
            month: '$_id.month',
            type: '$_id.type',
            value: '$totalValue',
            quantity: '$totalQuantity'
          }
        }
      ]);

      console.log('Monthly chart data from DB:', chartData);

      // If we have multiple years, we need to create datasets for each year
      if (years.length > 1) {
        // Format data for multi-year line chart
        const labels = Array.from({ length: 12 }, (_, i) => (i + 1).toString().padStart(2, '0'));
        const datasets = [];

        // Group data by year and type
        const yearTypeData = new Map();

        chartData.forEach(item => {
          const key = `${item.year}-${item.type}`;
          if (!yearTypeData.has(key)) {
            yearTypeData.set(key, Array(12).fill(0));
          }
          // Month is 1-based, array is 0-based
          yearTypeData.get(key)[item.month - 1] = item.quantity;
        });

        // Generate highly contrasting colors from completely different color families
        const generateColor = (index, isExport) => {
          // Predefined distinct colors from completely different color families
          const distinctColors = [
            'rgb(255, 0, 0)',      // Red
            'rgb(0, 128, 0)',      // Green
            'rgb(0, 0, 255)',      // Blue
            'rgb(255, 165, 0)',    // Orange
            'rgb(128, 0, 128)',    // Purple
            'rgb(0, 128, 128)',    // Teal
            'rgb(255, 0, 255)',    // Magenta
            'rgb(128, 128, 0)',    // Olive
            'rgb(165, 42, 42)',    // Brown
            'rgb(0, 0, 0)',        // Black
            'rgb(255, 192, 203)',  // Pink
            'rgb(64, 224, 208)',   // Turquoise
            'rgb(255, 215, 0)',    // Gold
            'rgb(75, 0, 130)',     // Indigo
            'rgb(173, 255, 47)',   // GreenYellow
            'rgb(220, 20, 60)',    // Crimson
          ];

          // For export, we'll use a darker variant of the same color
          const color = distinctColors[index % distinctColors.length];

          if (isExport) {
            // Parse the RGB values
            const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
            if (rgbMatch) {
              const r = Math.max(0, parseInt(rgbMatch[1]) - 40);
              const g = Math.max(0, parseInt(rgbMatch[2]) - 40);
              const b = Math.max(0, parseInt(rgbMatch[3]) - 40);
              return `rgb(${r}, ${g}, ${b})`;
            }
          }

          return color;
        };

        // Create datasets for each year and type
        let datasetIndex = 0;

        // First, determine which types to include
        const typesToInclude = [];
        if (!type) {
          typesToInclude.push('import', 'export');
        } else {
          typesToInclude.push(type);
        }

        // For each year and type, create a dataset
        years.forEach((year, yearIndex) => {
          typesToInclude.forEach(typeValue => {
            const key = `${year}-${typeValue}`;
            const data = yearTypeData.get(key) || Array(12).fill(0);

            const isExport = typeValue === 'export';
            const color = generateColor(yearIndex, isExport);

            datasets.push({
              label: `${year} ${typeValue.charAt(0).toUpperCase() + typeValue.slice(1)} Quantity`,
              data,
              borderColor: color,
              backgroundColor: color.replace(')', ', 0.2)').replace('hsl', 'hsla'),
              borderWidth: 2,
              tension: 0, // No curve, straight lines
              pointRadius: 4, // Show solid circle points
              pointHoverRadius: 6, // Slightly larger on hover
              pointBackgroundColor: 'white', // White fill
              pointBorderWidth: 2 // Border width
            });

            datasetIndex++;
          });
        });

        console.log('Monthly multi-year chart - Final datasets:', datasets);

        return NextResponse.json({
          chartType: 'line',
          labels,
          datasets,
          rawData: detailedData // 添加详细数据用于表格显示（包含unitPrice）
        });
      } else {
        // Single year chart (original implementation)
        // Format data for line chart
        const labels = [];
        const importValues = [];
        const exportValues = [];

        // Create a map for easy lookup
        const dataMap = new Map();

        chartData.forEach(item => {
          const key = `${item.year}-${item.month}`;
          if (!dataMap.has(key)) {
            dataMap.set(key, {
              label: `${item.year}-${item.month.toString().padStart(2, '0')}`,
              import: 0,
              export: 0
            });
          }

          if (item.type === 'import') {
            dataMap.get(key).import = item.quantity;
          } else {
            dataMap.get(key).export = item.quantity;
          }
        });

        // Sort by date
        const sortedData = Array.from(dataMap.values())
          .sort((a, b) => a.label.localeCompare(b.label));

        // Extract data for chart
        sortedData.forEach(item => {
          labels.push(item.label);
          importValues.push(item.import);
          exportValues.push(item.export);
        });

        // Create datasets based on the selected type
        const datasets = [];

        console.log(`Monthly chart - Type filter: ${type || 'none (All Types)'}`);
        console.log('Monthly chart - Import values:', importValues);
        console.log('Monthly chart - Export values:', exportValues);

        // Use completely different colors for better visibility
        const generateColor = (isExport) => {
          return isExport
            ? 'rgb(220, 20, 60)' // Crimson for export
            : 'rgb(0, 128, 0)'; // Green for import
        };

        // If type is not specified or type is 'import', add import dataset
        if (!type || type === 'import') {
          console.log('Monthly chart - Adding import dataset');
          const importColor = generateColor(false);
          datasets.push({
            label: `${years[0]} Import Quantity`,
            data: importValues,
            borderColor: importColor,
            backgroundColor: importColor.replace(')', ', 0.2)').replace('hsl', 'hsla'),
            borderWidth: 2,
            tension: 0, // No curve, straight lines
            pointRadius: 4, // Show solid circle points
            pointHoverRadius: 6, // Slightly larger on hover
            pointBackgroundColor: 'white', // White fill
            pointBorderWidth: 2 // Border width
          });
        }

        // If type is not specified or type is 'export', add export dataset
        if (!type || type === 'export') {
          console.log('Monthly chart - Adding export dataset');
          const exportColor = generateColor(true);
          datasets.push({
            label: `${years[0]} Export Quantity`,
            data: exportValues,
            borderColor: exportColor,
            backgroundColor: exportColor.replace(')', ', 0.2)').replace('hsl', 'hsla'),
            borderWidth: 2,
            tension: 0, // No curve, straight lines
            pointRadius: 4, // Show solid circle points
            pointHoverRadius: 6, // Slightly larger on hover
            pointBackgroundColor: 'white', // White fill
            pointBorderWidth: 2 // Border width
          });
        }

        console.log('Monthly chart - Final datasets:', datasets);

        return NextResponse.json({
          chartType: 'line',
          labels,
          datasets,
          rawData: detailedData // 添加详细数据用于表格显示（包含unitPrice）
        });
      }
    } else if (chartType === 'yearLine') {
      // For year line chart, group by year

      // If years are specified, filter by those years
      if (years.length > 0) {
        query.year = { $in: years };
      }

      // Get detailed data for price analysis
      const yearlyDetailedData = await TradeData.find(query)
        .select('year type quantity unitPrice value')
        .sort({ year: 1 })
        .lean();

      chartData = await TradeData.aggregate([
        { $match: query },
        {
          $group: {
            _id: {
              year: '$year',
              type: '$type'
            },
            totalQuantity: { $sum: '$quantity' }
          }
        },
        {
          $sort: {
            '_id.year': 1
          }
        },
        {
          $project: {
            _id: 0,
            year: '$_id.year',
            type: '$_id.type',
            quantity: '$totalQuantity'
          }
        }
      ]);

      // Format data for year line chart
      const labels = [];
      const datasets = [];

      // Get unique years
      const uniqueYears = [...new Set(chartData.map(item => item.year))].sort();

      // Create labels from years
      uniqueYears.forEach(year => {
        labels.push(year.toString());
      });

      // Determine which types to include based on the type filter
      const typesToInclude = [];
      if (!type) {
        // If no type filter, include both import and export
        typesToInclude.push('import', 'export');
        console.log('No type filter, including both import and export');
      } else {
        // Otherwise, only include the selected type
        typesToInclude.push(type);
        console.log(`Type filter: ${type}, only including ${type}`);
      }

      console.log('Types to include:', typesToInclude);
      console.log('Chart data:', chartData);

      // Create datasets for each type that should be included
      typesToInclude.forEach(typeValue => {
        console.log(`Processing type: ${typeValue}`);

        // When no type filter is applied (All Types), always create datasets for both types
        // When a specific type is selected, only create dataset for that type
        if (!type || type === typeValue) {
          const data = [];

          // For each year, find the corresponding data or use 0
          uniqueYears.forEach(year => {
            const item = chartData.find(d => d.year === year && d.type === typeValue);
            const value = item ? item.quantity : 0;
            console.log(`Year: ${year}, Type: ${typeValue}, Value: ${value}, Found: ${!!item}`);
            data.push(value);
          });

          console.log(`Adding dataset for ${typeValue}:`, data);

          // Use completely different colors for better visibility
          const generateColor = (isExport) => {
            return isExport
              ? 'rgb(255, 165, 0)' // Orange for export
              : 'rgb(75, 0, 130)'; // Indigo for import
          };

          const color = generateColor(typeValue === 'export');

          datasets.push({
            label: typeValue === 'import' ? 'Import Quantity' : 'Export Quantity',
            data,
            borderColor: color,
            backgroundColor: color.replace(')', ', 0.2)').replace('hsl', 'hsla'),
            borderWidth: 2,
            tension: 0, // No curve, straight lines
            pointRadius: 4, // Show solid circle points
            pointHoverRadius: 6, // Slightly larger on hover
            pointBackgroundColor: 'white', // White fill
            pointBorderWidth: 2 // Border width
          });
        } else {
          console.log(`Skipping dataset for ${typeValue} because type filter is ${type}`);
        }
      });

      console.log('Final datasets:', datasets);

      return NextResponse.json({
        chartType: 'yearLine',
        labels,
        datasets,
        rawData: yearlyDetailedData // 添加详细数据用于表格显示（包含unitPrice）
      });
    } else if (chartType === 'pie') {
      // For pie chart, determine what to group by
      let groupBy = 'product'; // Default

      if (product) {
        groupBy = 'destination'; // If product is specified, group by destination
      }

      if (searchParams.get('groupBy') === 'company') {
        groupBy = 'company'; // If groupBy=company is specified, group by company
      }

      if (searchParams.get('groupBy') === 'buyer') {
        groupBy = 'buyer'; // If groupBy=buyer is specified, group by buyer
      }

      console.log('PIE CHART API DEBUG - Pie chart query:', JSON.stringify(query));
      console.log('PIE CHART API DEBUG - Grouping by:', groupBy);
      console.log('PIE CHART API DEBUG - Chart type:', chartType);
      console.log('PIE CHART API DEBUG - Year parameter:', yearParam);
      console.log('PIE CHART API DEBUG - Parsed year:', year);

      chartData = await TradeData.aggregate([
        { $match: query },
        {
          $group: {
            _id: {
              key: `$${groupBy}`,
              type: '$type'
            },
            totalValue: { $sum: '$value' },
            totalQuantity: { $sum: '$quantity' }
          }
        },
        {
          $project: {
            _id: 0,
            key: '$_id.key',
            type: '$_id.type',
            value: '$totalValue',
            quantity: '$totalQuantity'
          }
        }
      ]);

      // Process data to limit items and combine the rest as "Others"
      // For company charts, limit to top 5
      // For product/destination charts, limit to top 5

      console.log('PIE CHART API DEBUG - Processing pie chart data');
      console.log('PIE CHART API DEBUG - Group by:', groupBy || 'destination');
      console.log('PIE CHART API DEBUG - Year filter:', year || 'All Years');
      console.log('PIE CHART API DEBUG - Original data count:', chartData.length);

      // Process for each type (import/export) separately
      const rawImportData = chartData.filter(item => item.type === 'import');
      const rawExportData = chartData.filter(item => item.type === 'export');

      // Function to process data for a type
      const processTopItems = (data: any[], limit: number) => {
        if (data.length <= limit) {
          console.log(`PIE CHART API DEBUG - Data count (${data.length}) is less than or equal to limit (${limit}), returning all items`);
          return data;
        }

        // Sort by quantity in descending order
        data.sort((a, b) => b.quantity - a.quantity);

        // Take top items
        const topItems = data.slice(0, limit);
        console.log(`PIE CHART API DEBUG - Top ${limit} items:`, topItems.map(item => item.key));

        // Combine the rest as "Others"
        const others = data.slice(limit).reduce(
          (acc, curr) => {
            acc.quantity += curr.quantity;
            acc.value += curr.value;
            return acc;
          },
          { key: 'Others', type: data[0].type, quantity: 0, value: 0 }
        );

        console.log(`PIE CHART API DEBUG - "Others" quantity: ${others.quantity}`);

        // Only include "Others" if it has a quantity > 0
        if (others.quantity > 0) {
          console.log('PIE CHART API DEBUG - Including "Others" in the result');
          return [...topItems, others];
        }

        console.log('PIE CHART API DEBUG - "Others" has zero quantity, not including in result');
        return topItems;
      };

      // Determine the limit based on the groupBy
      const limit = groupBy === 'company' ? 5 : 5;
      console.log(`PIE CHART API DEBUG - Processing ${groupBy || 'destination'} chart with limit: ${limit}`);

      // Log original data counts
      console.log(`PIE CHART API DEBUG - Original import data count: ${rawImportData.length}`);
      console.log(`PIE CHART API DEBUG - Original export data count: ${rawExportData.length}`);

      // Process import and export data
      const processedImport = processTopItems(rawImportData, limit);
      const processedExport = processTopItems(rawExportData, limit);

      // Log processed data counts
      console.log(`PIE CHART API DEBUG - Processed import data count: ${processedImport.length}`);
      console.log(`PIE CHART API DEBUG - Processed export data count: ${processedExport.length}`);

      // Log processed data keys
      console.log('PIE CHART API DEBUG - Processed import data keys:', processedImport.map(item => item.key));
      console.log('PIE CHART API DEBUG - Processed export data keys:', processedExport.map(item => item.key));

      // Combine processed data
      chartData = [...processedImport, ...processedExport];

      console.log('Pie chart data after processing:', chartData);

      // Format data for pie charts
      // We'll use maps to maintain consistent ordering and colors
      const importItems = [];
      const exportItems = [];

      // First, collect all items
      chartData.forEach(item => {
        if (item.type === 'import') {
          importItems.push({
            key: item.key,
            quantity: item.quantity
          });
        } else {
          exportItems.push({
            key: item.key,
            quantity: item.quantity
          });
        }
      });

      // Sort items by quantity in descending order for consistent initial ordering
      importItems.sort((a, b) => b.quantity - a.quantity);
      exportItems.sort((a, b) => b.quantity - a.quantity);

      // Extract labels and data
      const importLabels = importItems.map(item => item.key);
      const importData = importItems.map(item => item.quantity);
      const exportLabels = exportItems.map(item => item.key);
      const exportData = exportItems.map(item => item.quantity);

      // Create response object
      const response: any = {
        chartType: 'pie'
      };

      // Predefined distinct colors from completely different color families
      const distinctColors = [
        'rgb(255, 0, 0)',      // Red
        'rgb(0, 128, 0)',      // Green
        'rgb(0, 0, 255)',      // Blue
        'rgb(255, 165, 0)',    // Orange
        'rgb(128, 0, 128)',    // Purple
        'rgb(0, 128, 128)',    // Teal
        'rgb(255, 0, 255)',    // Magenta
        'rgb(128, 128, 0)',    // Olive
        'rgb(165, 42, 42)',    // Brown
        'rgb(0, 0, 0)',        // Black
        'rgb(255, 192, 203)',  // Pink
        'rgb(64, 224, 208)',   // Turquoise
        'rgb(255, 215, 0)',    // Gold
        'rgb(75, 0, 130)',     // Indigo
        'rgb(173, 255, 47)',   // GreenYellow
        'rgb(220, 20, 60)',    // Crimson
        'rgb(100, 149, 237)',  // CornflowerBlue
        'rgb(189, 183, 107)',  // DarkKhaki
        'rgb(85, 107, 47)',    // DarkOliveGreen
        'rgb(139, 0, 139)',    // DarkMagenta
      ];

      // Create a global color map to ensure consistent colors across different chart renders
      // We'll use the label as the key to ensure the same label always gets the same color
      const getColorForLabel = (label: string, isImport: boolean) => {
        // Special case for "Others" - always use gray
        if (label === 'Others') {
          return isImport ? 'rgb(128, 128, 128)' : 'rgb(169, 169, 169)';
        }

        // Generate a simple hash from the label string
        const hash = label.split('').reduce((acc, char) => {
          return acc + char.charCodeAt(0);
        }, 0);

        // Use the hash to select a color, with a different offset for import vs export
        const offset = isImport ? 0 : distinctColors.length / 2;
        const colorIndex = (hash + offset) % distinctColors.length;

        return distinctColors[colorIndex];
      };

      // Generate colors based on labels
      const generateColorsForLabels = (labels: string[], isImport: boolean) => {
        return labels.map(label => getColorForLabel(label, isImport));
      };

      // Add import data if type is not specified or type is 'import'
      if ((!type || type === 'import') && importLabels.length > 0) {
        response.import = {
          labels: importLabels,
          data: importData,
          backgroundColor: generateColorsForLabels(importLabels, true),
          year: yearParam // Include the year parameter in the response for debugging
        };
        console.log('PIE CHART API DEBUG - Adding import data with year:', yearParam);
        console.log('PIE CHART API DEBUG - Import data year type:', typeof response.import.year);
      }

      // Add export data if type is not specified or type is 'export'
      if ((!type || type === 'export') && exportLabels.length > 0) {
        response.export = {
          labels: exportLabels,
          data: exportData,
          backgroundColor: generateColorsForLabels(exportLabels, false),
          year: yearParam // Include the year parameter in the response for debugging
        };
        console.log('PIE CHART API DEBUG - Adding export data with year:', yearParam);
        console.log('PIE CHART API DEBUG - Export data year type:', typeof response.export.year);
      }

      return NextResponse.json(response);
    }

    return NextResponse.json(
      { error: 'Invalid chart type' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error generating chart data:', error);
    return NextResponse.json(
      { error: 'Failed to generate chart data' },
      { status: 500 }
    );
  }
}

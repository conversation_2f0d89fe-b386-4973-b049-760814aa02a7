import mongoose, { Schema, Document, Model } from 'mongoose';

export interface IFile extends Document {
  fileName: string;
  originalName: string;
  path: string;
  size: number;
  mimetype: string;
  uploadedBy: mongoose.Types.ObjectId;
  countries: string[];
  products: string[];
  years: number[];
  hsCodes: string[];
  dataCount: number;
  createdAt: Date;
  updatedAt: Date;
}

const FileSchema = new Schema<IFile>(
  {
    fileName: {
      type: String,
      required: [true, 'File name is required'],
    },
    originalName: {
      type: String,
      required: [true, 'Original file name is required'],
    },
    path: {
      type: String,
      required: [true, 'File path is required'],
    },
    size: {
      type: Number,
      required: [true, 'File size is required'],
    },
    mimetype: {
      type: String,
      required: [true, 'File mimetype is required'],
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Uploader information is required'],
    },
    countries: {
      type: [String],
      default: [],
    },
    products: {
      type: [String],
      default: [],
    },
    years: {
      type: [Number],
      default: [],
    },
    hsCodes: {
      type: [String],
      default: [],
    },
    dataCount: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

// Delete the model if it exists to prevent OverwriteModelError during hot reloads
const File = (mongoose.models.File as Model<IFile>) || mongoose.model<IFile>('File', FileSchema);

export default File;

import mongoose, { Schema, Document, Model } from 'mongoose';

export interface ITradeData extends Document {
  // Date information
  date: Date;
  year: number;
  month: number;

  // HS Code information
  hsCode: string;
  hsName: string;

  // Trade information
  type: 'import' | 'export';
  company: string;
  exportPort: string;
  destination: string;
  destinationPort: string;
  fileCountry: string;

  // Product information
  product: string;
  manufacturer: string;
  buyer: string;
  description: string;

  // Quantity and value
  quantity: number;
  unit: string;
  unitPrice: number;
  value: number;

  // System fields
  uploadedBy: mongoose.Types.ObjectId;
  fileName: string;
  createdAt: Date;
  updatedAt: Date;
}

const TradeDataSchema = new Schema<ITradeData>(
  {
    // Date information
    date: {
      type: Date,
      index: true,
    },
    year: {
      type: Number,
      required: [true, 'Year is required'],
      index: true,
    },
    month: {
      type: Number,
      required: [true, 'Month is required'],
      min: 1,
      max: 12,
      index: true,
    },

    // HS Code information
    hsCode: {
      type: String,
      index: true,
    },
    hsName: {
      type: String,
    },

    // Trade information
    type: {
      type: String,
      enum: ['import', 'export'],
      required: [true, 'Type is required'],
      index: true,
    },
    company: {
      type: String,
      index: true,
    },
    exportPort: {
      type: String,
    },
    destination: {
      type: String,
      index: true,
    },
    destinationPort: {
      type: String,
    },
    fileCountry: {
      type: String,
      index: true,
    },

    // Product information
    product: {
      type: String,
      required: [true, 'Product is required'],
      index: true,
    },
    manufacturer: {
      type: String,
    },
    buyer: {
      type: String,
    },
    description: {
      type: String,
    },

    // Quantity and value
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
    },
    unit: {
      type: String,
      required: [true, 'Unit is required'],
    },
    unitPrice: {
      type: Number,
      default: 0,
    },
    value: {
      type: Number,
      default: 0,
    },

    // System fields
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Uploader information is required'],
    },
    fileName: {
      type: String,
      required: [true, 'File name is required'],
    },
  },
  { timestamps: true }
);

// Create compound indexes for efficient querying
TradeDataSchema.index({ destination: 1, product: 1, type: 1, year: 1, month: 1 });
TradeDataSchema.index({ hsCode: 1, year: 1, month: 1 });
TradeDataSchema.index({ company: 1, product: 1 });
TradeDataSchema.index({ date: 1 });
TradeDataSchema.index({ fileCountry: 1, type: 1 });

// Delete the model if it exists to prevent OverwriteModelError during hot reloads
const TradeData = (mongoose.models.TradeData as Model<ITradeData>) || mongoose.model<ITradeData>('TradeData', TradeDataSchema);

export default TradeData;

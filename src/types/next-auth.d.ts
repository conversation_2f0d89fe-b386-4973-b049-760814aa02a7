import { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface User {
    id: string;
    username: string;
    email: string;
    role: string;
    permissions: {
      countries: string[];
      products: string[];
      years: string[];
      types: string[];
      fileCountries: string[];
      origins: string[];
      destinations: string[];
    };
  }

  interface Session extends DefaultSession {
    user: User & DefaultSession['user'];
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    username: string;
    role: string;
    permissions: {
      countries: string[];
      products: string[];
      years: string[];
      types: string[];
      fileCountries: string[];
      origins: string[];
      destinations: string[];
    };
  }
}

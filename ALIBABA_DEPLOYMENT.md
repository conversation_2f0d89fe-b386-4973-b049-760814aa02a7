# Deploying to Alibaba Cloud ECS

This guide provides step-by-step instructions for deploying the Trade Data Dashboard application to an Alibaba Cloud ECS (Elastic Compute Service) instance running Ubuntu.

## Prerequisites

1. An Alibaba Cloud account
2. An ECS instance with Ubuntu installed
3. A domain name (optional but recommended)
4. MongoDB Atlas account with a cluster set up

## Step 1: Set Up Your ECS Instance

1. Log in to your Alibaba Cloud Console
2. Create an ECS instance with Ubuntu (recommended: Ubuntu 20.04 or newer)
3. Configure security groups to allow HTTP (port 80), HTTPS (port 443), and SSH (port 22)
4. Connect to your instance via SSH

## Step 2: Deploy the Application

You can use the provided deployment script to automate most of the setup process:

1. Upload the deployment script to your server:
   ```bash
   scp deploy-alibaba.sh user@your-server-ip:~/
   ```

2. Connect to your server:
   ```bash
   ssh user@your-server-ip
   ```

3. Make the script executable:
   ```bash
   chmod +x deploy-alibaba.sh
   ```

4. Run the deployment script:
   ```bash
   ./deploy-alibaba.sh
   ```

5. Follow the prompts and instructions provided by the script

## Step 3: Configure Environment Variables

After running the deployment script, you need to update the environment variables:

1. Edit the `.env.local` file:
   ```bash
   nano /var/www/trade-dashboard/.env.local
   ```

2. Update the following variables:
   - `MONGODB_URI`: Your MongoDB Atlas connection string
   - `NEXTAUTH_URL`: Your domain name (e.g., https://your-domain.com)
   - `NEXTAUTH_SECRET`: A secure random string (already generated by the script)

## Step 4: Create an Admin User

1. Navigate to the application directory:
   ```bash
   cd /var/www/trade-dashboard
   ```

2. Run the create-admin script:
   ```bash
   npm run create-admin
   ```

3. Follow the prompts to create your admin user

## Step 5: Set Up SSL (Optional but Recommended)

To secure your application with HTTPS, you can use Let's Encrypt:

1. Install Certbot:
   ```bash
   sudo apt-get update
   sudo apt-get install certbot python3-certbot-nginx
   ```

2. Obtain and install a certificate:
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

3. Follow the prompts to complete the setup

## Step 6: Verify Deployment

1. Open your domain in a web browser
2. Log in with the admin credentials you created
3. Upload some trade data and verify that the charts and tables are working correctly

## Troubleshooting

### Application Not Starting

Check the PM2 logs:
```bash
pm2 logs trade-dashboard
```

### Nginx Configuration Issues

Check the Nginx error logs:
```bash
sudo tail -f /var/log/nginx/error.log
```

### MongoDB Connection Issues

Verify that your MongoDB Atlas IP whitelist includes your ECS instance's IP address.

## Maintenance

### Updating the Application

To update the application to the latest version:

```bash
cd /var/www/trade-dashboard
git pull
npm install
npm run build
pm2 restart trade-dashboard
```

### Monitoring

You can monitor your application using PM2:

```bash
pm2 monit
```

### Backups

Regularly back up your MongoDB data using Atlas's backup features.

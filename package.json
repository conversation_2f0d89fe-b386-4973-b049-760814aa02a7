{"name": "trade-data-dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup": "node scripts/setup.js", "create-admin": "node scripts/create-admin.js"}, "dependencies": {"axios": "^1.8.4", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "dotenv": "^16.5.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "multer": "^1.4.5-lts.2", "next": "15.3.1", "next-auth": "^4.24.11", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}
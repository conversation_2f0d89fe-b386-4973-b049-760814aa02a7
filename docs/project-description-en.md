# Trade Data Dashboard Project Description

## Project Overview

The Trade Data Dashboard is a web application based on Next.js, MongoDB Atlas, Mongoose, and Alibaba Cloud ECS Ubuntu, designed for displaying and analyzing trade data. The application provides intuitive data visualization and management features to help users understand trade trends and patterns.

## Technology Stack

- **Frontend Framework**: Next.js
- **Database**: MongoDB Atlas
- **Database ODM**: Mongoose
- **Server Environment**: Alibaba Cloud ECS (Ubuntu)
- **Authentication**: NextAuth.js
- **Data Visualization**: Chart.js
- **Styling**: Tailwind CSS

## Main Features

### Admin Features

1. **User Management**
   - Create, edit, and delete users
   - Set user permissions (countries, products, years, types)
   - Assign user roles (admin or regular user)

2. **Data Upload**
   - Upload trade data in Excel format
   - View and manage uploaded files
   - Delete uploaded files

### User Features

1. **Data Dashboard**
   - Display trade data based on user permissions
   - Filter data using various filters (country, product, import/export type)
   - View trade data table, including buyer information

2. **Data Visualization**
   - Monthly quantity trend charts (with year selection)
   - Yearly quantity trend charts (with multi-year selection)
   - Product/destination distribution pie charts (with year selection)
   - Company distribution pie charts (showing top 10 companies, rest grouped as "Others")

3. **Data Export**
   - Export filtered data as Excel files

## Permission System

The system uses an "AND" mode (AND logic) for permission control:

1. **Permission Categories**
   - Countries
   - Products
   - Years
   - Types (import/export)

2. **Permission Logic**
   - Users can only access data that meets all permission conditions simultaneously
   - For example: If a user has permissions for Country A, Product B, Year 2023, and Type "import", they can only view import data for Product B in Country A from 2023
   - If a permission category is set to empty (no selection), the user will not be able to access any data for that category

3. **Admin Permissions**
   - Admins can access all data without permission restrictions
   - Admins can manage all users and permission settings

## Data Structure

Trade data includes the following fields:

- Month
- HS code
- Product name
- Export/Import type
- Company
- Buyer
- Ports
- Quantity
- Unit
- Unit price
- Value
- Year
- Destination/Origin country

## Special Features

1. **Chart Optimization**
   - Hide empty distribution charts
   - Trade data table limited to 10 rows with a "more" button at the bottom
   - Chart size optimization, supporting side-by-side display
   - Distribution charts display percentage values

2. **Search Functionality**
   - Dropdown lists support search functionality for users to quickly find desired options

## Deployment Information

The project is deployed on Alibaba Cloud ECS Ubuntu server, using PM2 for process management and Nginx as a reverse proxy server.

## Usage Instructions

1. **Login to the System**
   - Use the username and password provided by the administrator to log in

2. **Browse Data**
   - Use filters to select data of interest
   - View various charts and tables to understand trade trends

3. **Export Data**
   - Click the "Download Excel" button to export the currently filtered data

4. **Manage Users** (Admin only)
   - Access the "/admin/users" page to manage users and permissions
   - When setting user permissions, note the impact of empty permissions

## Notes

1. When permissions are set to empty arrays, users will not be able to access any data for that category
2. Charts display quantity data, not value data
3. Monthly and yearly trend charts operate independently, without affecting each other
4. Distribution charts have independent year selection functionality

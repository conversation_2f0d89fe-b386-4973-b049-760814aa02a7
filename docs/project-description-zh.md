# 贸易数据仪表盘项目说明

## 项目概述

贸易数据仪表盘是一个基于 Next.js、MongoDB Atlas、Mongoose 和 Alibaba Cloud ECS Ubuntu 的 Web 应用程序，用于展示和分析贸易数据。该应用程序提供了直观的数据可视化和管理功能，帮助用户了解贸易趋势和模式。

## 技术栈

- **前端框架**：Next.js
- **数据库**：MongoDB Atlas
- **数据库 ODM**：Mongoose
- **服务器环境**：Alibaba Cloud ECS (Ubuntu)
- **认证**：NextAuth.js
- **数据可视化**：Chart.js
- **样式**：Tailwind CSS

## 主要功能

### 管理员功能

1. **用户管理**
   - 创建、编辑和删除用户
   - 设置用户权限（国家、产品、年份、类型）
   - 分配用户角色（管理员或普通用户）

2. **数据上传**
   - 上传 Excel 格式的贸易数据
   - 查看和管理已上传的文件
   - 删除已上传的文件

### 用户功能

1. **数据仪表盘**
   - 基于用户权限显示贸易数据
   - 使用多种筛选器（国家、产品、进出口类型）过滤数据
   - 查看贸易数据表格，包括买家信息

2. **数据可视化**
   - 月度数量趋势图表（可选择年份）
   - 年度数量趋势图表（可多选年份）
   - 产品/目的地分布饼图（可选择年份）
   - 公司分布饼图（显示前 10 家公司，其余归为"其他"）

3. **数据导出**
   - 将筛选后的数据导出为 Excel 文件

## 权限系统

系统采用"且"模式（AND 逻辑）的权限控制：

1. **权限类别**
   - 国家（countries）
   - 产品（products）
   - 年份（years）
   - 类型（types：进口/出口）

2. **权限逻辑**
   - 用户只能访问同时满足所有权限条件的数据
   - 例如：如果用户有国家 A、产品 B、年份 2023 和类型"进口"的权限，则只能查看 2023 年国家 A 中产品 B 的进口数据
   - 如果某个权限类别设置为空（无选择），用户将无法访问该类别的任何数据

3. **管理员权限**
   - 管理员可以访问所有数据，不受权限限制
   - 管理员可以管理所有用户和权限设置

## 数据结构

贸易数据包含以下字段：

- 月份（Month）
- HS 编码（HS code）
- 产品名称（Product name）
- 进出口类型（Export/Import type）
- 公司（Company）
- 买家（Buyer）
- 港口（Ports）
- 数量（Quantity）
- 单位（Unit）
- 单价（Unit price）
- 价值（Value）
- 年份（Year）
- 目的地/来源国（Destination/Origin country）

## 特殊功能

1. **图表优化**
   - 隐藏空的分布图表
   - 贸易数据表格限制为 10 行，底部有"更多"按钮
   - 图表大小优化，支持并排显示
   - 分布图表显示百分比值

2. **搜索功能**
   - 下拉列表支持搜索功能，方便用户快速找到所需选项

## 部署说明

项目部署在 Alibaba Cloud ECS Ubuntu 服务器上，使用 PM2 进行进程管理，Nginx 作为反向代理服务器。

## 使用说明

1. **登录系统**
   - 使用管理员提供的用户名和密码登录

2. **浏览数据**
   - 使用筛选器选择感兴趣的数据
   - 查看各种图表和表格了解贸易趋势

3. **导出数据**
   - 点击"下载 Excel"按钮导出当前筛选的数据

4. **管理用户**（仅管理员）
   - 访问"/admin/users"页面管理用户和权限
   - 设置用户权限时，请注意空权限的影响

## 注意事项

1. 权限设置为空数组时，用户将无法访问该类别的任何数据
2. 图表显示的是数量数据，而非价值数据
3. 月度和年度趋势图表独立运行，互不影响
4. 分布图表有独立的年份选择功能
